package ui

import (
	"fmt"
	"strconv"
	"time"

	"bella/pkg/copier"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// showConfigForm displays the configuration management form
func (a *App) showConfigForm() {
	pageName := "configForm"

	form := tview.NewForm().
		AddInputField("Profile Name", "", 30, nil, nil).
		AddInputField("Description", "", 50, nil, nil).
		AddDropDown("Compression Type", []string{"tar.gz", "tar.xz", "tar.bz2", "tar.zst", "zip"}, 0, nil).
		AddDropDown("Compression Level", []string{"Good", "Better", "Best"}, 0, nil).
		AddInputField("Block Size (MB)", "4", 10, nil, nil).
		AddCheckbox("Verify After Copy", false, nil).
		AddCheckbox("Sparse Copy", false, nil).
		AddCheckbox("Skip Bad Sectors", false, nil).
		AddCheckbox("Use Copy Offload", true, nil).
		AddCheckbox("Preserve Attributes", true, nil).
		AddDropDown("Checksum", []string{"none", "sha256", "md5", "sha1"}, 0, nil)

	a.applyFormTheme(form)

	form.AddButton("Save Profile", func() {
		profileName := form.GetFormItem(0).(*tview.InputField).GetText()
		if profileName == "" {
			a.showDetailedError("Save Error", fmt.Errorf("please enter a profile name"))
			return
		}

		description := form.GetFormItem(1).(*tview.InputField).GetText()
		if description == "" {
			description = fmt.Sprintf("Profile created on %s", time.Now().Format("2006-01-02 15:04:05"))
		}

		_, compressionType := form.GetFormItem(2).(*tview.DropDown).GetCurrentOption()
		_, compressionLevel := form.GetFormItem(3).(*tview.DropDown).GetCurrentOption()

		blockSizeStr := form.GetFormItem(4).(*tview.InputField).GetText()
		blockSizeMB, err := strconv.Atoi(blockSizeStr)
		if err != nil || blockSizeMB <= 0 {
			a.showDetailedError("Save Error", fmt.Errorf("invalid block size: must be a positive number"))
			return
		}
		blockSize := blockSizeMB * 1024 * 1024

		verify := form.GetFormItem(5).(*tview.Checkbox).IsChecked()
		sparse := form.GetFormItem(6).(*tview.Checkbox).IsChecked()
		skipBad := form.GetFormItem(7).(*tview.Checkbox).IsChecked()
		useCopyOffload := form.GetFormItem(8).(*tview.Checkbox).IsChecked()
		preserveAttrs := form.GetFormItem(9).(*tview.Checkbox).IsChecked()
		_, checksum := form.GetFormItem(10).(*tview.DropDown).GetCurrentOption()
		if checksum == "none" {
			checksum = ""
		}

		profile := copier.ConfigProfile{
			Name:               profileName,
			Description:        description,
			CompressionType:    compressionType,
			CompressionLevel:   compressionLevel,
			BlockSize:          blockSize,
			Verify:             verify,
			Sparse:             sparse,
			SkipBadSectors:     skipBad,
			UseCopyOffload:     useCopyOffload,
			PreserveAttributes: preserveAttrs,
			Checksum:           checksum,
		}

		config, err := copier.LoadConfigFile()
		if err != nil {
			a.showDetailedError("Save Error", fmt.Errorf("failed to load config file: %w", err))
			return
		}

		if err := config.AddProfile(profile); err != nil {
			a.showDetailedError("Save Error", err)
			return
		}

		if err := config.SaveConfigFile(); err != nil {
			a.showDetailedError("Save Error", fmt.Errorf("failed to save config file: %w", err))
			return
		}

		a.showDetailedError("Success", fmt.Errorf("profile '%s' saved successfully", profileName))
	})

	form.AddButton("Load Profile", func() {
		a.showProfileSelector(pageName, func(profile *copier.ConfigProfile) {
			// Update form fields with profile values
			form.GetFormItem(0).(*tview.InputField).SetText(profile.Name)
			form.GetFormItem(1).(*tview.InputField).SetText(profile.Description)

			// Set compression type
			compressionDropDown := form.GetFormItem(2).(*tview.DropDown)
			compressionTypes := []string{"tar.gz", "tar.xz", "tar.bz2", "tar.zst", "zip"}
			for i, ct := range compressionTypes {
				if ct == profile.CompressionType {
					compressionDropDown.SetCurrentOption(i)
					break
				}
			}

			// Set compression level
			levelDropDown := form.GetFormItem(3).(*tview.DropDown)
			levels := []string{"Good", "Better", "Best"}
			for i, level := range levels {
				if level == profile.CompressionLevel {
					levelDropDown.SetCurrentOption(i)
					break
				}
			}

			// Set block size in MB
			blockSizeMB := profile.BlockSize / (1024 * 1024)
			form.GetFormItem(4).(*tview.InputField).SetText(strconv.Itoa(blockSizeMB))

			// Set checkboxes
			form.GetFormItem(5).(*tview.Checkbox).SetChecked(profile.Verify)
			form.GetFormItem(6).(*tview.Checkbox).SetChecked(profile.Sparse)
			form.GetFormItem(7).(*tview.Checkbox).SetChecked(profile.SkipBadSectors)
			form.GetFormItem(8).(*tview.Checkbox).SetChecked(profile.UseCopyOffload)
			form.GetFormItem(9).(*tview.Checkbox).SetChecked(profile.PreserveAttributes)

			// Set checksum
			checksumDropDown := form.GetFormItem(10).(*tview.DropDown)
			checksums := []string{"none", "sha256", "md5", "sha1"}
			checksumValue := profile.Checksum
			if checksumValue == "" {
				checksumValue = "none"
			}
			for i, cs := range checksums {
				if cs == checksumValue {
					checksumDropDown.SetCurrentOption(i)
					break
				}
			}

			a.switchToPage(pageName)
		})
	})

	form.AddButton("Create Defaults", func() {
		config, err := copier.CreateDefaultProfiles()
		if err != nil {
			a.showDetailedError("Create Error", err)
			return
		}

		if err := config.SaveConfigFile(); err != nil {
			a.showDetailedError("Create Error", fmt.Errorf("failed to save default profiles: %w", err))
			return
		}

		a.showDetailedError("Success", fmt.Errorf("default profiles created successfully"))
	})

	form.AddButton("Cleanup Temp Files", func() {
		if err := copier.CleanupOldTempFiles(24 * time.Hour); err != nil {
			a.showDetailedError("Cleanup Error", fmt.Errorf("failed to cleanup temporary files: %w", err))
			return
		}

		a.showDetailedError("Success", fmt.Errorf("temporary file cleanup completed successfully"))
	})

	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Configuration Profiles")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showProfileSelector displays a list of available profiles for selection
func (a *App) showProfileSelector(returnPage string, onSelect func(*copier.ConfigProfile)) {
	pageName := "profileSelector"

	config, err := copier.LoadConfigFile()
	if err != nil {
		a.showDetailedError("Load Error", fmt.Errorf("failed to load config file: %w", err))
		return
	}

	profiles := config.ListProfiles()
	if len(profiles) == 0 {
		a.showDetailedError("No Profiles", fmt.Errorf("no profiles found. Create some profiles first or use 'Create Defaults'"))
		return
	}

	list := tview.NewList()
	for _, name := range profiles {
		profile, _ := config.GetProfile(name)
		list.AddItem(name, profile.Description, 0, func() {
			selectedProfile, _ := config.GetProfile(name)
			a.pages.RemovePage(pageName)
			onSelect(selectedProfile)
		})
	}

	list.SetBorder(true).SetTitle("Select Profile")
	list.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.pages.RemovePage(pageName)
			a.switchToPage(returnPage)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, list, true, true)
}
