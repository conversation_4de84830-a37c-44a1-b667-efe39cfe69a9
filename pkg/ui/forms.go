package ui

import (
	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// applyFormTheme styles a form and its dropdowns for readability
func (a *App) applyFormTheme(form *tview.Form) {
	for i := 0; i < form.GetFormItemCount(); i++ {
		item := form.GetFormItem(i)
		if dropdown, ok := item.(*tview.DropDown); ok {
			dropdown.SetFieldTextColor(a.theme.PrimaryText)
			dropdown.SetFieldBackgroundColor(a.theme.SecondaryBg)
			dropdown.SetListStyles(
				tcell.StyleDefault.Foreground(a.theme.PrimaryText).Background(a.theme.SecondaryBg),
				tcell.StyleDefault.Foreground(a.theme.PrimaryText).Background(a.theme.SelectedColor),
			)
		}
	}
}
