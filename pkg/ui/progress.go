package ui

import (
	"fmt"
	"strings"

	"bella/pkg/copier"
	"bella/pkg/progress"

	"github.com/rivo/tview"
)

// runOperation launches the progress screen and the backend logic.
// THIS IS THE ORIGINAL, STABLE IMPLEMENTATION.
func (a *App) runOperation(cfg *copier.Config) {
	progressPageName := "progressModal"

	modal := tview.NewFlex().SetDirection(tview.FlexRow)
	modal.SetBorder(true).SetTitle("Operation in Progress")

	stageText := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)
	progressBar := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)
	statsText := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)

	modal.AddItem(stageText, 1, 0, false).
		AddItem(progressBar, 3, 0, false).
		AddItem(statsText, 0, 1, false)

	frame := tview.NewFrame(modal).SetBorders(0, 0, 0, 0, 0, 0)

	centeredFlex := tview.NewFlex().
		AddItem(nil, 0, 1, false).
		AddItem(tview.NewFlex().SetDirection(tview.FlexRow).
			AddItem(nil, 0, 1, false).
			AddItem(frame, 10, 0, true).
			AddItem(nil, 0, 1, false),
			80, 0, true).
		AddItem(nil, 0, 1, false)

	a.pages.AddPage(progressPageName, centeredFlex, true, true)

	progressChan := make(chan progress.Info, 100)
	cfg.ProgressChan = progressChan
	cfg.Progress = true

	operationResult := make(chan error, 1)

	go func() {
		defer close(progressChan)
		operationResult <- copier.Execute(cfg)
	}()

	go func() {
		var lastSummary string
		for p := range progressChan {
			currentProgress := p
			a.app.QueueUpdateDraw(func() {
				if !currentProgress.IsFinished {
					titleColor := a.theme.colorToHex(a.theme.PrimaryText)
					stageColor := a.theme.colorToHex(a.theme.InfoColor)
					speedColor := a.theme.colorToHex(a.theme.AccentText)

					modal.SetTitle(fmt.Sprintf("[%s]Operation in Progress", titleColor))

					stageText.SetText(fmt.Sprintf("[%s]Stage: [%s]%s", stageColor, titleColor, titleCase(currentProgress.Stage)))
					progressBar.SetText(a.createImprovedProgressBar(currentProgress.PercentDone)) // Using improved progress bar
					stats := fmt.Sprintf(
						"[%s]Speed:[%s] %.2f MB/s\n[%s]ETA:[%s] %s\n[%s]Data:[%s] %s / %s",
						speedColor, titleColor, currentProgress.AvgSpeed,
						speedColor, titleColor, currentProgress.ETA,
						speedColor, titleColor,
						progress.HumanizeBytes(uint64(currentProgress.BytesDone)),
						progress.HumanizeBytes(uint64(currentProgress.BytesTotal)),
					)
					statsText.SetText(stats)
				}
				if currentProgress.Summary != "" {
					lastSummary = currentProgress.Summary
				}
			})
		}

		err := <-operationResult
		a.app.QueueUpdateDraw(func() {
			a.pages.RemovePage(progressPageName)
			a.showResultModal(err, lastSummary)
		})
	}()
}

// showResultModal is unchanged.
func (a *App) showResultModal(err error, summary string) {
	resultPageName := "resultModal"
	var message string
	errorColor := a.theme.colorToHex(a.theme.ErrorColor)
	successColor := a.theme.colorToHex(a.theme.SuccessColor)
	textColor := a.theme.colorToHex(a.theme.PrimaryText)

	if err != nil {
		message = fmt.Sprintf("[%s]Operation Failed:\n\n[%s]%v", errorColor, textColor, err)
	} else {
		message = fmt.Sprintf("[%s]✔ All Stages Complete\n\n[%s]%s", successColor, textColor, summary)
	}

	modal := tview.NewModal().
		SetText(message).
		AddButtons([]string{"OK"}).
		SetBackgroundColor(a.theme.ModalBg).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			if a.pages.HasPage("main") {
				a.pages.RemovePage(resultPageName)
				a.switchToPage("main")
			} else {
				a.app.Stop()
			}
		})

	a.pages.AddPage(resultPageName, modal, true, true)
}

// createProgressBarOriginal is the original, stable progress bar function.
func (a *App) createProgressBarOriginal(percent float64) string {
	barWidth := 40
	filled := int(percent * float64(barWidth) / 100.0)
	if filled < 0 {
		filled = 0
	}
	if filled > barWidth {
		filled = barWidth
	}

	fillColor := a.theme.colorToHex(a.theme.ProgressFill)
	emptyColor := a.theme.colorToHex(a.theme.ProgressEmpty)
	textColor := a.theme.colorToHex(a.theme.ProgressText)

	bar := fmt.Sprintf("[%s]%s[%s]%s", fillColor, strings.Repeat("█", filled), emptyColor, strings.Repeat("█", barWidth-filled))
	return fmt.Sprintf("%s\n[%s]%.1f%%", bar, textColor, percent)
}

// createImprovedProgressBar creates a progress bar with solid background fill to hide underlying content
func (a *App) createImprovedProgressBar(percent float64) string {
	barWidth := 50
	filled := int(percent * float64(barWidth) / 100.0)
	if filled < 0 {
		filled = 0
	}
	if filled > barWidth {
		filled = barWidth
	}

	// Use solid background colors that completely hide underlying content
	fillColor := a.theme.colorToHex(a.theme.ProgressFill)
	emptyColor := a.theme.colorToHex(a.theme.ProgressEmpty)
	textColor := a.theme.colorToHex(a.theme.ProgressText)

	// Create progress bar with solid background fill
	filledPart := fmt.Sprintf("[%s::%s]%s", fillColor, fillColor, strings.Repeat(" ", filled))
	emptyPart := fmt.Sprintf("[%s::%s]%s", emptyColor, emptyColor, strings.Repeat(" ", barWidth-filled))

	// Add border characters for better visibility
	bar := fmt.Sprintf("[white::]▐%s%s▌", filledPart, emptyPart)

	// Center the percentage text
	percentText := fmt.Sprintf("[%s::b]%.1f%%", textColor, percent)

	return fmt.Sprintf("%s\n%s", bar, percentText)
}

// RunCLIProgress provides an improved CLI progress renderer using Tview
func RunCLIProgress(cfg *copier.Config) error {
	cliApp := NewApp()
	progressPageName := "cliProgress"

	// Create a more compact and stable layout
	mainContainer := tview.NewFlex().SetDirection(tview.FlexRow)

	// Header with operation title
	headerText := tview.NewTextView().
		SetDynamicColors(true).
		SetTextAlign(tview.AlignCenter).
		SetText("[white::b]Bella File Operations")

	// Stage indicator
	stageText := tview.NewTextView().
		SetDynamicColors(true).
		SetTextAlign(tview.AlignCenter)

	// Progress bar with solid background fill
	progressBar := tview.NewTextView().
		SetDynamicColors(true).
		SetTextAlign(tview.AlignCenter)

	// Statistics in a more compact format
	statsText := tview.NewTextView().
		SetDynamicColors(true).
		SetTextAlign(tview.AlignCenter)

	// Add components with fixed heights to prevent layout shifts
	mainContainer.
		AddItem(headerText, 1, 0, false).
		AddItem(stageText, 1, 0, false).
		AddItem(progressBar, 3, 0, false).
		AddItem(statsText, 3, 0, false)

	// Center the container with padding
	centeredFlex := tview.NewFlex().
		AddItem(nil, 0, 1, false).
		AddItem(tview.NewFlex().SetDirection(tview.FlexRow).
			AddItem(nil, 0, 1, false).
			AddItem(mainContainer, 9, 0, true).
			AddItem(nil, 0, 1, false),
			70, 0, true).
		AddItem(nil, 0, 1, false)

	cliApp.pages.AddPage(progressPageName, centeredFlex, true, true)
	cliApp.app.SetRoot(cliApp.pages, true)

	progressChan := make(chan progress.Info, 100)
	cfg.ProgressChan = progressChan
	cfg.Progress = true

	operationResult := make(chan error, 1)

	go func() {
		defer close(progressChan)
		operationResult <- copier.Execute(cfg)
	}()

	go func() {
		var lastSummary string
		for p := range progressChan {
			currentProgress := p
			cliApp.app.QueueUpdateDraw(func() {
				if !currentProgress.IsFinished {
					// Update stage with consistent formatting
					stageText.SetText(fmt.Sprintf("[cyan::b]Stage: [white::]%s", titleCase(currentProgress.Stage)))

					// Create improved progress bar with solid background
					progressBar.SetText(cliApp.createImprovedProgressBar(currentProgress.PercentDone))

					// Compact stats display that replaces existing content
					stats := fmt.Sprintf(
						"[yellow::b]Speed: [white::]%.2f MB/s  [yellow::b]ETA: [white::]%s\n[yellow::b]Progress: [white::]%s / %s",
						currentProgress.AvgSpeed,
						currentProgress.ETA,
						progress.HumanizeBytes(uint64(currentProgress.BytesDone)),
						progress.HumanizeBytes(uint64(currentProgress.BytesTotal)),
					)
					statsText.SetText(stats)
				}
				if currentProgress.Summary != "" {
					lastSummary = currentProgress.Summary
				}
			})
		}

		err := <-operationResult
		cliApp.app.QueueUpdateDraw(func() {
			cliApp.pages.RemovePage(progressPageName)
			cliApp.showResultModal(err, lastSummary)
		})
	}()

	return cliApp.app.Run()
}
