package ui

import (
	"fmt"
	"strconv"
	"strings"

	"bella/pkg/copier"
	"bella/pkg/device"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// showVerifyForm displays the form for verify operations.
func (a *App) showVerifyForm() {
	pageName := "verifyForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Source", "", 40, nil, nil).
		AddInputField("Target", "", 40, nil, nil).
		AddInputField("Block Size", "auto", 10, nil, nil).
		AddDropDown("Checksum", []string{"none", "sha256", "md5", "sha1"}, 0, nil)

	a.applyFormTheme(form)

	form.AddButton("Browse Source", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})
	form.AddButton("Browse Target", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(1).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Verify", func() {
		cfg.Operation = copier.OpVerify
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()

		bsText := form.GetFormItem(2).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Input)
			if err != nil {
				a.showDetailedError("Auto Block Size", err)
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showDetailedError("Invalid Block Size", err)
				return
			}
			cfg.BlockSize = bs
		}

		_, checksumAlgorithm := form.GetFormItem(3).(*tview.DropDown).GetCurrentOption()
		if checksumAlgorithm == "none" {
			cfg.Checksum = ""
		} else {
			cfg.Checksum = checksumAlgorithm
		}

		a.runOperation(cfg)
	})

	form.AddButton("Back", func() { a.goBack(pageName) })
	form.SetBorder(true).SetTitle(" Verify Files ")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showWipeForm displays the form for wipe operations.
func (a *App) showWipeForm() {
	pageName := "wipeForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Target Device", "", 40, nil, nil).
		AddDropDown("Mode", []string{"zero", "random"}, 1, nil).
		AddInputField("Passes", "1", 3, nil, nil).
		AddInputField("Block Size", "4M", 10, nil, nil)

	a.applyFormTheme(form)

	form.AddButton("Browse Target", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			form.GetFormItem(3).(*tview.InputField).SetText("auto")
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Wipe", func() {
		cfg.Operation = copier.OpWipe
		cfg.Output = form.GetFormItem(0).(*tview.InputField).GetText()
		_, cfg.WipeMode = form.GetFormItem(1).(*tview.DropDown).GetCurrentOption()
		passes, _ := strconv.Atoi(form.GetFormItem(2).(*tview.InputField).GetText())
		if passes < 1 {
			passes = 1
		}
		cfg.WipePasses = passes

		bsText := form.GetFormItem(3).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Output)
			if err != nil {
				a.showDetailedError("Auto Block Size", err)
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showDetailedError("Invalid Block Size", err)
				return
			}
			cfg.BlockSize = bs
		}

		a.showConfirmation(fmt.Sprintf("This will IRREVERSIBLY DESTROY ALL DATA on %s.\nAre you sure?", cfg.Output), func() {
			a.runOperation(cfg)
		})
	})

	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Wipe Operation")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}
