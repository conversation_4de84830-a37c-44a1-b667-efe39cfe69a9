package ui

import (
	"fmt"
	"strings"

	"bella/pkg/copier"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// showCompressForm displays a standalone compression form.
func (a *App) showCompressForm() {
	pageName := "compressForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Input", "", 40, nil, nil).
		AddInputField("Output (without extension)", "", 40, nil, nil).
		AddPasswordField("Password (ZIP only)", "", 40, '*', nil)

	compressionTypeDropDown := tview.NewDropDown().
		SetLabel("Compression Type").
		SetOptions([]string{"tar.gz", "tar.xz", "tar.bz2", "tar.zst", "zip"}, nil)

	compressionLevelDropDown := tview.NewDropDown().
		SetLabel("Compression Level").
		SetOptions([]string{"Good", "Better", "Best"}, nil)

	compressionTypeDropDown.SetSelectedFunc(func(text string, index int) {
		if text == "zip" {
			// ZIP compression level is handled internally by the zip library
			compressionLevelDropDown.SetLabel("Compression Level").
				SetOptions([]string{"N/A"}, nil).
				SetCurrentOption(0)
		} else {
			// tar.gz and tar.xz support compression levels
			compressionLevelDropDown.SetLabel("Compression Level").
				SetOptions([]string{"Good", "Better", "Best"}, nil).
				SetCurrentOption(0)
		}
	})

	form.AddFormItem(compressionTypeDropDown).
		AddFormItem(compressionLevelDropDown)

	a.applyFormTheme(form)

	form.AddButton("Preview", func() {
		inputPath := form.GetFormItem(0).(*tview.InputField).GetText()
		if inputPath == "" {
			a.showDetailedError("Preview Error", fmt.Errorf("please specify an input path"))
			return
		}

		_, compressionType := compressionTypeDropDown.GetCurrentOption()
		_, level := compressionLevelDropDown.GetCurrentOption()
		if level == "N/A" {
			level = "Good"
		}

		preview, err := copier.EstimateCompression(inputPath, compressionType, level)
		if err != nil {
			a.showDetailedError("Preview Error", err)
			return
		}

		a.showPreviewDialog(preview)
	})

	form.AddButton("Start Compression", func() {
		cfg.Operation = copier.OpCopy
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		// For standalone compression, let ensureCompressionExtension add the proper suffix
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()
		cfg.Password = form.GetFormItem(2).(*tview.InputField).GetText()
		cfg.Compression = "compress"
		_, cfg.CompressionType = compressionTypeDropDown.GetCurrentOption()
		_, level := compressionLevelDropDown.GetCurrentOption()
		if level == "N/A" {
			cfg.CompressionLevel = "Good"
		} else {
			cfg.CompressionLevel = level
		}

		// Validate password usage
		if cfg.Password != "" && cfg.CompressionType != "zip" {
			a.showDetailedError("Password Error", fmt.Errorf("password protection is only supported for ZIP archives"))
			return
		}

		// If input is a directory, CreateArchive path logic will enforce extension.
		a.runOperation(cfg)
	})

	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Standalone Compression")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showDecompressForm displays a standalone decompression form.
func (a *App) showDecompressForm() {
	pageName := "decompressForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Archive Input", "", 40, nil, nil).
		AddInputField("Output Directory", "", 40, nil, nil).
		AddPasswordField("Password (if encrypted)", "", 40, '*', nil)

	compressionTypeDropDown := tview.NewDropDown().
		SetLabel("Archive Type").
		SetOptions([]string{"auto", "tar.gz", "tar.xz", "tar.bz2", "tar.zst", "zip"}, nil)

	a.applyFormTheme(form)
	form.AddFormItem(compressionTypeDropDown)

	form.AddButton("Start Extraction", func() {
		cfg.Operation = copier.OpCopy
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()
		cfg.Password = form.GetFormItem(2).(*tview.InputField).GetText()
		cfg.Compression = "decompress"
		_, typ := compressionTypeDropDown.GetCurrentOption()
		if typ == "auto" {
			cfg.Compression = "auto"
			cfg.CompressionType = ""
		} else {
			cfg.CompressionType = typ
		}
		a.runOperation(cfg)
	})

	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Standalone Decompression")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showBrowseForm displays the archive browsing form.
func (a *App) showBrowseForm() {
	pageName := "browseForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Archive File", "", 40, nil, nil)

	compressionTypeDropDown := tview.NewDropDown().
		SetLabel("Archive Type").
		SetOptions([]string{"auto", "tar.gz", "tar.xz", "tar.bz2", "tar.zst", "zip"}, nil)

	a.applyFormTheme(form)
	form.AddFormItem(compressionTypeDropDown)

	form.AddButton("Browse Archive", func() {
		cfg.Operation = copier.OpBrowse
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		_, typ := compressionTypeDropDown.GetCurrentOption()
		if typ == "auto" {
			cfg.Compression = "auto"
			cfg.CompressionType = ""
		} else {
			cfg.CompressionType = typ
		}
		cfg.Progress = false // No progress needed for browsing

		// Run browse operation
		a.runOperation(cfg)
	})

	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Browse Archive Contents")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showSelectiveExtractForm displays the selective extraction form
func (a *App) showSelectiveExtractForm() {
	pageName := "selectiveExtractForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Archive File", "", 40, nil, nil).
		AddInputField("Output Directory", "", 40, nil, nil).
		AddTextArea("Files to Extract", "", 40, 5, 0, nil)

	compressionTypeDropDown := tview.NewDropDown().
		SetLabel("Archive Type").
		SetOptions([]string{"auto", "tar.gz", "tar.xz", "tar.bz2", "tar.zst", "zip"}, nil)

	a.applyFormTheme(form)
	form.AddFormItem(compressionTypeDropDown)

	form.AddButton("Browse Archive First", func() {
		archivePath := form.GetFormItem(0).(*tview.InputField).GetText()
		if archivePath == "" {
			a.showDetailedError("Browse Error", fmt.Errorf("please specify an archive file path"))
			return
		}

		// Create a temporary config for browsing
		browseCfg := copier.NewConfig()
		browseCfg.Operation = copier.OpBrowse
		browseCfg.Input = archivePath
		_, typ := compressionTypeDropDown.GetCurrentOption()
		if typ == "auto" {
			browseCfg.Compression = "auto"
			browseCfg.CompressionType = ""
		} else {
			browseCfg.CompressionType = typ
		}
		browseCfg.Progress = false

		// For now, just show a message to browse manually
		a.showDetailedError("Browse Archive", fmt.Errorf("please browse the archive manually using the Browse Archive option from the main menu, then enter the file paths you want to extract in the text area below (one per line)"))
	})

	form.AddButton("Start Selective Extraction", func() {
		cfg.Operation = copier.OpSelectiveExtract
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()

		// Parse files from text area
		textArea := form.GetFormItem(2).(*tview.TextArea)
		filesText := textArea.GetText()
		if filesText == "" {
			a.showDetailedError("Selection Error", fmt.Errorf("please specify files to extract"))
			return
		}

		files := strings.Split(filesText, "\n")
		var selectedFiles []string
		for _, file := range files {
			file = strings.TrimSpace(file)
			if file != "" {
				selectedFiles = append(selectedFiles, file)
			}
		}

		if len(selectedFiles) == 0 {
			a.showDetailedError("Selection Error", fmt.Errorf("no valid files specified"))
			return
		}

		cfg.SelectedFiles = selectedFiles

		_, typ := compressionTypeDropDown.GetCurrentOption()
		if typ == "auto" {
			cfg.Compression = "auto"
			cfg.CompressionType = ""
		} else {
			cfg.CompressionType = typ
		}

		a.runOperation(cfg)
	})

	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Selective Archive Extraction")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}
