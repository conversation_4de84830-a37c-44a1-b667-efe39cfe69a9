package ui

import (
	"fmt"
	"strconv"
	"strings"

	"bella/pkg/copier"
	"bella/pkg/device"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// showCopyForm displays the form for copy operations.
func (a *App) showCopyForm() {
	pageName := "copyForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Input", "", 40, nil, nil).
		AddInputField("Output", "", 40, nil, nil).
		AddInputField("Block Size", "auto", 10, nil, nil).
		AddInputField("Count", "-1", 10, nil, nil).
		AddInputField("Skip", "0", 10, nil, nil).
		AddInputField("Seek", "0", 10, nil, nil).
		AddDropDown("Compression", []string{"none", "compress", "decompress", "auto"}, 0, nil)

	compressionTypeDropDown := tview.NewDropDown().
		SetLabel("Compression Type").
		SetOptions([]string{"tar.gz", "tar.xz", "tar.bz2", "tar.zst", "zip"}, nil)

	compressionLevelDropDown := tview.NewDropDown().
		SetLabel("Compression Level").
		SetOptions([]string{"Good", "Better", "Best"}, nil)

	compressionTypeDropDown.SetSelectedFunc(func(text string, index int) {
		if text == "zip" {
			compressionLevelDropDown.SetLabel("Compression Level").
				SetOptions([]string{"N/A"}, nil).
				SetCurrentOption(0)
		} else {
			compressionLevelDropDown.SetLabel("Compression Level").
				SetOptions([]string{"Good", "Better", "Best"}, nil).
				SetCurrentOption(0)
		}
	})

	form.AddFormItem(compressionTypeDropDown).
		AddFormItem(compressionLevelDropDown).
		AddDropDown("Checksum", []string{"none", "sha256", "md5", "sha1"}, 0, nil).
		AddCheckbox("Sparse Copy", false, nil).
		AddCheckbox("Skip Bad Sectors", false, nil).
		AddCheckbox("Verify After Copy", false, nil).
		AddCheckbox("Append Mode", false, nil).
		AddCheckbox("Disable Kernel Copy Offload", false, nil).
		AddCheckbox("Dry Run", false, nil)

	a.applyFormTheme(form)

	form.AddButton("Browse Input", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})
	form.AddButton("Browse Output", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(1).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Preview Compression", func() {
		inputPath := form.GetFormItem(0).(*tview.InputField).GetText()
		if inputPath == "" {
			a.showDetailedError("Preview Error", fmt.Errorf("please specify an input path"))
			return
		}

		_, compression := form.GetFormItem(6).(*tview.DropDown).GetCurrentOption()
		if compression != "compress" {
			a.showDetailedError("Preview Error", fmt.Errorf("compression preview is only available when compression mode is set to 'compress'"))
			return
		}

		_, compressionType := compressionTypeDropDown.GetCurrentOption()
		_, level := compressionLevelDropDown.GetCurrentOption()
		if level == "N/A" {
			level = "Good"
		}

		preview, err := copier.EstimateCompression(inputPath, compressionType, level)
		if err != nil {
			a.showDetailedError("Preview Error", err)
			return
		}

		a.showPreviewDialog(preview)
	})

	form.AddButton("Start Copy", func() {
		cfg.Operation = copier.OpCopy
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()

		bsText := form.GetFormItem(2).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Input)
			if err != nil {
				a.showDetailedError("Auto Block Size", err)
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showDetailedError("Invalid Block Size", err)
				return
			}
			cfg.BlockSize = bs
		}

		count, _ := strconv.Atoi(form.GetFormItem(3).(*tview.InputField).GetText())
		cfg.Count = count
		skip, _ := strconv.ParseInt(form.GetFormItem(4).(*tview.InputField).GetText(), 10, 64)
		cfg.Skip = skip
		seek, _ := strconv.ParseInt(form.GetFormItem(5).(*tview.InputField).GetText(), 10, 64)
		cfg.Seek = seek

		_, cfg.Compression = form.GetFormItem(6).(*tview.DropDown).GetCurrentOption()
		_, cfg.CompressionType = compressionTypeDropDown.GetCurrentOption()
		_, compressionLevel := compressionLevelDropDown.GetCurrentOption()

		// Handle ZIP compression level (N/A case)
		if compressionLevel == "N/A" {
			cfg.CompressionLevel = "Good" // Default fallback, though not used for ZIP
		} else {
			cfg.CompressionLevel = compressionLevel
		}

		checksumDropDown := form.GetFormItemByLabel("Checksum").(*tview.DropDown)
		_, checksumAlgorithm := checksumDropDown.GetCurrentOption()
		if checksumAlgorithm == "none" {
			cfg.Checksum = ""
		} else {
			cfg.Checksum = checksumAlgorithm
		}

		cfg.Sparse = form.GetFormItemByLabel("Sparse Copy").(*tview.Checkbox).IsChecked()
		cfg.SkipBadSectors = form.GetFormItemByLabel("Skip Bad Sectors").(*tview.Checkbox).IsChecked()
		cfg.Verify = form.GetFormItemByLabel("Verify After Copy").(*tview.Checkbox).IsChecked()
		cfg.Append = form.GetFormItemByLabel("Append Mode").(*tview.Checkbox).IsChecked()
		cfg.UseCopyOffload = !form.GetFormItemByLabel("Disable Kernel Copy Offload").(*tview.Checkbox).IsChecked()
		cfg.DryRun = form.GetFormItemByLabel("Dry Run").(*tview.Checkbox).IsChecked()

		a.runOperation(cfg)
	})
	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Copy Operation")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}
