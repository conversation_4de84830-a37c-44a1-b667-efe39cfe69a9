package copier

import (
	"archive/tar"
	"archive/zip"
	"compress/gzip"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"

	"bella/pkg/progress"

	bzip2Writer "github.com/dsnet/compress/bzip2"
	"github.com/klauspost/compress/zstd"
	"github.com/ulikunitz/xz"
)

// appendCompressedFile compresses and appends the content of a temporary file to the final output file
func appendCompressedFile(tempPath, finalPath string, cfg *Config) error {
	tempFile, err := os.Open(tempPath)
	if err != nil {
		return fmt.Errorf("failed to open temporary file for compression: %w", err)
	}
	defer tempFile.Close()

	finalFile, err := os.OpenFile(finalPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("failed to open final file for compressed append: %w", err)
	}
	defer finalFile.Close()

	var writer io.Writer = finalFile
	var closer io.Closer

	switch cfg.CompressionType {
	case "gzip":
		level, err := mapCompressionLevel(cfg.CompressionLevel, "gzip")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		gw, err := gzip.NewWriterLevel(writer, level)
		if err != nil {
			return fmt.Errorf("failed to create gzip writer for append: %w", err)
		}
		writer = gw
		closer = gw
	case "xz":
		// Use WriterConfig for xz compression levels in append mode too
		config := xz.WriterConfig{}
		level, err := mapCompressionLevel(cfg.CompressionLevel, "xz")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		// Map compression level to xz properties
		if level >= 9 {
			config.DictCap = 64 * 1024 * 1024 // 64MB for best compression
		} else if level >= 7 {
			config.DictCap = 16 * 1024 * 1024 // 16MB for better compression
		} else {
			config.DictCap = 8 * 1024 * 1024 // 8MB for good compression
		}
		xzw, err := config.NewWriter(writer)
		if err != nil {
			return fmt.Errorf("failed to create xz writer for append: %w", err)
		}
		writer = xzw
		closer = xzw
	case "bzip2":
		level, err := mapCompressionLevel(cfg.CompressionLevel, "bzip2")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		config := &bzip2Writer.WriterConfig{Level: level}
		bw, err := bzip2Writer.NewWriter(writer, config)
		if err != nil {
			return fmt.Errorf("failed to create bzip2 writer for append: %w", err)
		}
		writer = bw
		closer = bw
	case "zstd":
		level, err := mapCompressionLevel(cfg.CompressionLevel, "zstd")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		// Map compression level to zstd level for append mode
		var zstdLevel zstd.EncoderLevel
		if level >= 9 {
			zstdLevel = zstd.SpeedBestCompression
		} else if level >= 7 {
			zstdLevel = zstd.SpeedBetterCompression
		} else {
			zstdLevel = zstd.SpeedDefault
		}
		zw, err := zstd.NewWriter(writer, zstd.WithEncoderLevel(zstdLevel))
		if err != nil {
			return fmt.Errorf("failed to create zstd writer for append: %w", err)
		}
		writer = zw
		closer = zw
	case "tar.gz":
		// Note: Appending to tar archives is complex and not commonly supported
		return fmt.Errorf("tar.gz compression append is not supported for individual files")
	case "tar.xz":
		// Note: Appending to tar archives is complex and not commonly supported
		return fmt.Errorf("tar.xz compression append is not supported for individual files")
	case "zip":
		// Note: ZIP append is complex and not commonly used for single files
		// For now, we'll treat it as an error
		return fmt.Errorf("ZIP compression append is not supported for individual files")
	default:
		// No compression, write directly to output
	}

	if closer != nil {
		defer closer.Close()
	}

	_, err = io.Copy(writer, tempFile)
	if err != nil {
		return fmt.Errorf("failed to compress and append temp file content: %w", err)
	}

	return finalFile.Sync()
}

// compressFile compresses an input file to an output file
func compressFile(inputPath, outputPath string, cfg *Config) error {
	in, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input for compression: %w", err)
	}
	defer in.Close()

	out, err := createFileWithMetadata(inputPath, outputPath, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, cfg.PreserveAttributes)
	if err != nil {
		return fmt.Errorf("failed to create compressed output: %w", err)
	}
	defer out.Close()

	info, err := in.Stat()
	if err != nil {
		return fmt.Errorf("failed to get file info for compression: %w", err)
	}
	totalSize := info.Size()

	var writer io.Writer = out
	var closer io.Closer

	switch cfg.CompressionType {
	case "gzip":
		level, err := mapCompressionLevel(cfg.CompressionLevel, "gzip")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		gw, err := gzip.NewWriterLevel(writer, level)
		if err != nil {
			return fmt.Errorf("failed to create gzip writer: %w", err)
		}
		writer = gw
		closer = gw
	case "xz":
		// Use WriterConfig to support compression levels for xz
		config := xz.WriterConfig{}
		level, err := mapCompressionLevel(cfg.CompressionLevel, "xz")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		// Map compression level to xz properties
		if level >= 9 {
			config.DictCap = 64 * 1024 * 1024 // 64MB for best compression
		} else if level >= 7 {
			config.DictCap = 16 * 1024 * 1024 // 16MB for better compression
		} else {
			config.DictCap = 8 * 1024 * 1024 // 8MB for good compression
		}
		xzw, err := config.NewWriter(writer)
		if err != nil {
			return fmt.Errorf("failed to create xz writer: %w", err)
		}
		writer = xzw
		closer = xzw
	case "bzip2":
		level, err := mapCompressionLevel(cfg.CompressionLevel, "bzip2")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		config := &bzip2Writer.WriterConfig{Level: level}
		bw, err := bzip2Writer.NewWriter(writer, config)
		if err != nil {
			return fmt.Errorf("failed to create bzip2 writer: %w", err)
		}
		writer = bw
		closer = bw
	case "zstd":
		level, err := mapCompressionLevel(cfg.CompressionLevel, "zstd")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		// Map compression level to zstd level (1-19, with 3 being default)
		var zstdLevel zstd.EncoderLevel
		if level >= 9 {
			zstdLevel = zstd.SpeedBestCompression // Level 19
		} else if level >= 7 {
			zstdLevel = zstd.SpeedBetterCompression // Level 6
		} else {
			zstdLevel = zstd.SpeedDefault // Level 3
		}
		zw, err := zstd.NewWriter(writer, zstd.WithEncoderLevel(zstdLevel))
		if err != nil {
			return fmt.Errorf("failed to create zstd writer: %w", err)
		}
		writer = zw
		closer = zw
	case "tar.gz":
		// For single file tar.gz compression, create a proper tar archive with gzip compression
		level, err := mapCompressionLevel(cfg.CompressionLevel, "gzip")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		gw, err := gzip.NewWriterLevel(writer, level)
		if err != nil {
			return fmt.Errorf("failed to create gzip writer: %w", err)
		}
		tw := tar.NewWriter(gw)

		// Create tar header for the single file
		originalFilename := filepath.Base(cfg.Input)
		header := &tar.Header{
			Name: originalFilename,
			Mode: 0644,
			Size: totalSize,
		}
		if err := tw.WriteHeader(header); err != nil {
			gw.Close()
			return fmt.Errorf("failed to write tar header: %w", err)
		}

		writer = tw
		closer = &tarGzCloser{tw: tw, gw: gw}
	case "tar.xz":
		// For single file tar.xz compression, create a proper tar archive with xz compression
		xzw, err := xz.NewWriter(writer)
		if err != nil {
			return fmt.Errorf("failed to create xz writer: %w", err)
		}
		tw := tar.NewWriter(xzw)

		// Create tar header for the single file
		originalFilename := filepath.Base(cfg.Input)
		header := &tar.Header{
			Name: originalFilename,
			Mode: 0644,
			Size: totalSize,
		}
		if err := tw.WriteHeader(header); err != nil {
			xzw.Close()
			return fmt.Errorf("failed to write tar header: %w", err)
		}

		writer = tw
		closer = &tarXzCloser{tw: tw, xzw: xzw}
	case "tar.bz2":
		// For single file tar.bz2 compression, create a proper tar archive with bzip2 compression
		level, err := mapCompressionLevel(cfg.CompressionLevel, "bzip2")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		config := &bzip2Writer.WriterConfig{Level: level}
		bzw, err := bzip2Writer.NewWriter(writer, config)
		if err != nil {
			return fmt.Errorf("failed to create bzip2 writer: %w", err)
		}
		tw := tar.NewWriter(bzw)

		// Create tar header for the single file
		originalFilename := filepath.Base(cfg.Input)
		header := &tar.Header{
			Name: originalFilename,
			Mode: 0644,
			Size: totalSize,
		}
		if err := tw.WriteHeader(header); err != nil {
			bzw.Close()
			return fmt.Errorf("failed to write tar header: %w", err)
		}

		writer = tw
		closer = &tarBz2Closer{tw: tw, bzw: bzw}
	case "tar.zst":
		// For single file tar.zst compression, create a proper tar archive with zstd compression
		level, err := mapCompressionLevel(cfg.CompressionLevel, "zstd")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		// Map compression level to zstd level
		var zstdLevel zstd.EncoderLevel
		if level >= 9 {
			zstdLevel = zstd.SpeedBestCompression
		} else if level >= 7 {
			zstdLevel = zstd.SpeedBetterCompression
		} else {
			zstdLevel = zstd.SpeedDefault
		}
		zw, err := zstd.NewWriter(writer, zstd.WithEncoderLevel(zstdLevel))
		if err != nil {
			return fmt.Errorf("failed to create zstd writer: %w", err)
		}
		tw := tar.NewWriter(zw)

		// Create tar header for the single file
		originalFilename := filepath.Base(cfg.Input)
		header := &tar.Header{
			Name: originalFilename,
			Mode: 0644,
			Size: totalSize,
		}
		if err := tw.WriteHeader(header); err != nil {
			zw.Close()
			return fmt.Errorf("failed to write tar header: %w", err)
		}

		writer = tw
		closer = &tarZstCloser{tw: tw, zw: zw}
	case "zip":
		// For single file zip compression, we need to create a zip archive with one file
		zw := zip.NewWriter(writer)
		// Use the original input filename from cfg.Input, not the temp file path
		originalFilename := filepath.Base(cfg.Input)
		// Remove any existing compression extension for the internal filename
		if filepath.Ext(originalFilename) == ".gz" || filepath.Ext(originalFilename) == ".xz" {
			originalFilename = originalFilename[:len(originalFilename)-len(filepath.Ext(originalFilename))]
		}
		zipFile, err := zw.Create(originalFilename)
		if err != nil {
			return fmt.Errorf("failed to create zip file entry: %w", err)
		}
		writer = zipFile
		closer = zw
	default:
		// No compression, write directly to output
	}

	if closer != nil {
		defer closer.Close()
	}

	reporter := progress.NewReporter("Compressing", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize)
	}

	// Determine if we should use parallel I/O optimization
	// Use parallel I/O for large files (>10MB) and when we have multiple cores
	useParallelIO := totalSize > 10*1024*1024 && runtime.NumCPU() > 1

	var compressionErr error
	if useParallelIO {
		compressionErr = compressFileWithParallelIO(in, writer, totalSize, cfg, reporter)
	} else {
		compressionErr = compressFileSequential(in, writer, totalSize, cfg, reporter)
	}

	if compressionErr != nil {
		return compressionErr
	}

	// Calculate compression ratio if this is a compression operation
	if cfg.Progress && (cfg.CompressionType != "" && cfg.CompressionType != "none") {
		compressedSize, sizeErr := getFileSize(cfg.Output)
		if sizeErr == nil {
			reporter.FinishCompression(totalSize, totalSize, compressedSize)
		} else {
			reporter.Finish(totalSize)
		}
	} else if cfg.Progress {
		reporter.Finish(totalSize)
	}

	return nil
}
