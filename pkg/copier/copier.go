package copier

import (
	"fmt"
	"os"
	"strings"
)

// preprocessConfig handles filename correction and smart duplicate checks.
func preprocessConfig(cfg *Config) error {
	if cfg.Operation != OpCopy {
		return nil
	}

	info, err := os.Stat(cfg.Input)
	if err != nil {
		return fmt.Errorf("could not access input '%s': %w", cfg.Input, err)
	}

	isCompressing := (cfg.Compression == "compress" || cfg.Compression == "auto") && info.IsDir()

	finalOutput := cfg.Output
	isArchive := false

	if isCompressing {
		isArchive = true
		ext := "." + cfg.CompressionType
		// Trim existing known archive extensions from the user's provided output string.
		base := strings.TrimSuffix(cfg.Output, ".zip")
		base = strings.TrimSuffix(base, ".tar.gz")
		base = strings.TrimSuffix(base, ".tar.xz")
		base = strings.TrimSuffix(base, ".tar.bz2")
		base = strings.TrimSuffix(base, ".tar.zst")
		finalOutput = base + ext
	}

	// Now check for duplicates using the final, correct filename.
	if _, err := os.Stat(finalOutput); err == nil {
		var action string
		var promptErr error

		// For archives, 'append' and 'verify' don't make sense.
		if isArchive {
			action, promptErr = promptForFileAction(finalOutput, cfg.IsUIMode, true)
		} else if !cfg.Append { // For regular files, only prompt if not in append mode.
			action, promptErr = promptForFileAction(finalOutput, cfg.IsUIMode, false)
		}

		if promptErr != nil {
			return promptErr
		}

		switch action {
		case "cancel":
			return fmt.Errorf("operation cancelled by user")
		case "append":
			cfg.Append = true
		case "verify":
			cfg.Operation = OpVerify
		}
	}

	// Commit the potentially changed output path back to the config.
	cfg.Output = finalOutput
	return nil
}

func Execute(cfg *Config) error {
	// Ensure temporary files are cleaned up when the operation completes
	defer func() {
		if err := CleanupTempFiles(); err != nil {
			fmt.Printf("Warning: failed to cleanup temporary files: %v\n", err)
		}
	}()

	if err := preprocessConfig(cfg); err != nil {
		return err
	}

	if cfg.DryRun {
		fmt.Printf("Dry run: would perform operation with config: %+v\n", cfg)
		return nil
	}

	switch cfg.Operation {
	case OpCopy:
		info, _ := os.Stat(cfg.Input)
		if info.IsDir() {
			isCompressing := cfg.Compression == "compress" || (cfg.Compression == "auto" &&
				(strings.HasSuffix(cfg.Output, ".zip") || strings.HasSuffix(cfg.Output, ".tar.gz") ||
					strings.HasSuffix(cfg.Output, ".tar.xz") || strings.HasSuffix(cfg.Output, ".tar.bz2") ||
					strings.HasSuffix(cfg.Output, ".tar.zst")))
			if isCompressing {
				return CreateArchive(cfg)
			}
			return copyDirectory(cfg)
		}
		return copyFile(cfg)
	case OpWipe:
		return doWipe(cfg)
	case OpVerify:
		return doVerify(cfg)
	case OpBrowse:
		return BrowseArchive(cfg)
	case OpSelectiveExtract:
		return SelectiveExtractArchive(cfg)
	default:
		return fmt.Errorf("no operation specified or recognized")
	}
}
