package copier

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// TempFileManager manages temporary files and ensures cleanup
type TempFileManager struct {
	tempFiles []string
	tempDirs  []string
	mutex     sync.Mutex
	basePath  string
}

// NewTempFileManager creates a new temporary file manager
func NewTempFileManager() *TempFileManager {
	return &TempFileManager{
		tempFiles: make([]string, 0),
		tempDirs:  make([]string, 0),
		basePath:  os.TempDir(),
	}
}

// SetBasePath sets the base directory for temporary files
func (tm *TempFileManager) SetBasePath(path string) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	tm.basePath = path
}

// CreateTempFile creates a temporary file with the given prefix and suffix
func (tm *TempFileManager) CreateTempFile(prefix, suffix string) (string, error) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	// Generate a unique filename
	timestamp := time.Now().UnixNano()
	filename := fmt.Sprintf("%s_%d_%s", prefix, timestamp, suffix)
	tempPath := filepath.Join(tm.basePath, filename)

	// Create the file
	file, err := os.Create(tempPath)
	if err != nil {
		return "", fmt.Errorf("failed to create temporary file: %w", err)
	}
	file.Close()

	// Track the file for cleanup
	tm.tempFiles = append(tm.tempFiles, tempPath)
	return tempPath, nil
}

// CreateTempDir creates a temporary directory with the given prefix
func (tm *TempFileManager) CreateTempDir(prefix string) (string, error) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	// Generate a unique directory name
	timestamp := time.Now().UnixNano()
	dirname := fmt.Sprintf("%s_%d", prefix, timestamp)
	tempPath := filepath.Join(tm.basePath, dirname)

	// Create the directory
	if err := os.MkdirAll(tempPath, 0755); err != nil {
		return "", fmt.Errorf("failed to create temporary directory: %w", err)
	}

	// Track the directory for cleanup
	tm.tempDirs = append(tm.tempDirs, tempPath)
	return tempPath, nil
}

// GetTempPath generates a temporary file path without creating the file
func (tm *TempFileManager) GetTempPath(prefix, suffix string) string {
	timestamp := time.Now().UnixNano()
	filename := fmt.Sprintf("%s_%d_%s", prefix, timestamp, suffix)
	return filepath.Join(tm.basePath, filename)
}

// TrackFile adds an existing file to the cleanup list
func (tm *TempFileManager) TrackFile(path string) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	tm.tempFiles = append(tm.tempFiles, path)
}

// TrackDir adds an existing directory to the cleanup list
func (tm *TempFileManager) TrackDir(path string) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	tm.tempDirs = append(tm.tempDirs, path)
}

// RemoveFile removes a specific file and removes it from tracking
func (tm *TempFileManager) RemoveFile(path string) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	// Remove the file
	err := os.Remove(path)
	if err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to remove temporary file %s: %w", path, err)
	}

	// Remove from tracking list
	for i, file := range tm.tempFiles {
		if file == path {
			tm.tempFiles = append(tm.tempFiles[:i], tm.tempFiles[i+1:]...)
			break
		}
	}

	return nil
}

// CleanupAll removes all tracked temporary files and directories
func (tm *TempFileManager) CleanupAll() error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	var errors []string

	// Clean up files
	for _, file := range tm.tempFiles {
		if err := os.Remove(file); err != nil && !os.IsNotExist(err) {
			errors = append(errors, fmt.Sprintf("failed to remove temp file %s: %v", file, err))
		}
	}

	// Clean up directories
	for _, dir := range tm.tempDirs {
		if err := os.RemoveAll(dir); err != nil && !os.IsNotExist(err) {
			errors = append(errors, fmt.Sprintf("failed to remove temp directory %s: %v", dir, err))
		}
	}

	// Clear the tracking lists
	tm.tempFiles = tm.tempFiles[:0]
	tm.tempDirs = tm.tempDirs[:0]

	if len(errors) > 0 {
		return fmt.Errorf("cleanup errors: %s", strings.Join(errors, "; "))
	}

	return nil
}

// GetTrackedFiles returns a copy of the tracked files list
func (tm *TempFileManager) GetTrackedFiles() []string {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	
	files := make([]string, len(tm.tempFiles))
	copy(files, tm.tempFiles)
	return files
}

// GetTrackedDirs returns a copy of the tracked directories list
func (tm *TempFileManager) GetTrackedDirs() []string {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	
	dirs := make([]string, len(tm.tempDirs))
	copy(dirs, tm.tempDirs)
	return dirs
}

// CleanupOldTempFiles removes old temporary files from the system temp directory
func CleanupOldTempFiles(maxAge time.Duration) error {
	tempDir := os.TempDir()
	
	entries, err := os.ReadDir(tempDir)
	if err != nil {
		return fmt.Errorf("failed to read temp directory: %w", err)
	}

	cutoff := time.Now().Add(-maxAge)
	var errors []string

	for _, entry := range entries {
		// Look for files that match our naming pattern
		if strings.Contains(entry.Name(), "bella") && 
		   (strings.HasSuffix(entry.Name(), ".tmp") || 
		    strings.HasSuffix(entry.Name(), ".tmp.bella") ||
		    strings.Contains(entry.Name(), "_temp_")) {
			
			fullPath := filepath.Join(tempDir, entry.Name())
			info, err := entry.Info()
			if err != nil {
				continue
			}

			if info.ModTime().Before(cutoff) {
				if entry.IsDir() {
					if err := os.RemoveAll(fullPath); err != nil {
						errors = append(errors, fmt.Sprintf("failed to remove old temp dir %s: %v", fullPath, err))
					}
				} else {
					if err := os.Remove(fullPath); err != nil {
						errors = append(errors, fmt.Sprintf("failed to remove old temp file %s: %v", fullPath, err))
					}
				}
			}
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("cleanup errors: %s", strings.Join(errors, "; "))
	}

	return nil
}

// Global temp manager instance
var globalTempManager = NewTempFileManager()

// GetGlobalTempManager returns the global temporary file manager
func GetGlobalTempManager() *TempFileManager {
	return globalTempManager
}

// CreateTempFile creates a temporary file using the global manager
func CreateTempFile(prefix, suffix string) (string, error) {
	return globalTempManager.CreateTempFile(prefix, suffix)
}

// CreateTempDir creates a temporary directory using the global manager
func CreateTempDir(prefix string) (string, error) {
	return globalTempManager.CreateTempDir(prefix)
}

// CleanupTempFiles cleans up all temporary files using the global manager
func CleanupTempFiles() error {
	return globalTempManager.CleanupAll()
}
