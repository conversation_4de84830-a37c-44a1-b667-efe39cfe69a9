package copier

import (
	"archive/tar"
	"compress/gzip"
	"io"

	"github.com/ulikunitz/xz"
)

// tarGzCloser handles closing both tar and gzip writers in the correct order
type tarGzCloser struct {
	tw *tar.Writer
	gw *gzip.Writer
}

func (c *tarGzCloser) Close() error {
	if err := c.tw.Close(); err != nil {
		c.gw.Close() // Try to close gzip writer even if tar fails
		return err
	}
	return c.gw.Close()
}

// tarXzCloser handles closing both tar and xz writers in the correct order
type tarXzCloser struct {
	tw  *tar.Writer
	xzw *xz.Writer
}

func (c *tarXzCloser) Close() error {
	if err := c.tw.Close(); err != nil {
		c.xzw.Close() // Try to close xz writer even if tar fails
		return err
	}
	return c.xzw.Close()
}

// tarBz2Closer handles closing both tar and bzip2 writers in the correct order
type tarBz2Closer struct {
	tw  *tar.Writer
	bzw io.Closer
}

func (c *tarBz2Closer) Close() error {
	if err := c.tw.Close(); err != nil {
		c.bzw.Close() // Try to close bzip2 writer even if tar fails
		return err
	}
	return c.bzw.Close()
}

// tarZstCloser handles closing both tar and zstd writers in the correct order
type tarZstCloser struct {
	tw *tar.Writer
	zw io.Closer
}

func (c *tarZstCloser) Close() error {
	if err := c.tw.Close(); err != nil {
		c.zw.Close() // Try to close zstd writer even if tar fails
		return err
	}
	return c.zw.Close()
}

// readResult represents the result of a read operation
type readResult struct {
	data []byte
	err  error
}

// writeRequest represents a write request with a result channel
type writeRequest struct {
	data   []byte
	result chan<- writeResult
}

// writeResult represents the result of a write operation
type writeResult struct {
	bytesWritten int
	err          error
}
