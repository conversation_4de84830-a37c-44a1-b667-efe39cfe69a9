package copier

import (
	"fmt"
	"io"
	"sync"

	"bella/pkg/progress"
)

// compressFileSequential performs traditional sequential compression
func compressFileSequential(in io.Reader, writer io.Writer, totalSize int64, cfg *Config, reporter *progress.Reporter) error {
	// Use a larger buffer for better performance with large files
	bufferSize := cfg.BlockSize
	if bufferSize < 64*1024 {
		bufferSize = 64 * 1024 // Minimum 64KB buffer
	}
	if bufferSize > 1024*1024 {
		bufferSize = 1024 * 1024 // Maximum 1MB buffer to avoid excessive memory usage
	}

	buf := make([]byte, bufferSize)
	var written int64
	for {
		n, err := in.Read(buf)
		if n > 0 {
			if _, wErr := writer.Write(buf[:n]); wErr != nil {
				return fmt.Errorf("compression write error: %w", wErr)
			}
			written += int64(n)
			if cfg.Progress {
				reporter.Update(written, totalSize)
			}
		}
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("compression read error: %w", err)
		}
	}

	// Note: Finish is called by the parent function to include compression ratio
	return nil
}

// compressFileWithParallelIO performs compression with parallel I/O optimization
func compressFileWithParallelIO(in io.Reader, writer io.Writer, totalSize int64, cfg *Config, reporter *progress.Reporter) error {
	// Use double buffering to overlap I/O and compression
	// This approach keeps the compression algorithm sequential but optimizes I/O

	bufferSize := cfg.BlockSize
	if bufferSize < 256*1024 {
		bufferSize = 256 * 1024 // Minimum 256KB buffer for parallel I/O
	}
	if bufferSize > 2*1024*1024 {
		bufferSize = 2 * 1024 * 1024 // Maximum 2MB buffer
	}

	// Create two buffers for double buffering
	buf1 := make([]byte, bufferSize)
	buf2 := make([]byte, bufferSize)

	// Channels for coordinating read/write operations
	readChan := make(chan readResult, 2)
	writeChan := make(chan writeRequest, 2)

	var wg sync.WaitGroup
	var written int64

	// Start reader goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer close(readChan)

		useFirstBuffer := true
		for {
			var currentBuf []byte
			if useFirstBuffer {
				currentBuf = buf1
			} else {
				currentBuf = buf2
			}

			n, err := in.Read(currentBuf)
			readChan <- readResult{data: currentBuf[:n], err: err}

			if err != nil {
				break
			}

			// Switch buffers
			useFirstBuffer = !useFirstBuffer
		}
	}()

	// Start writer goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()

		for req := range writeChan {
			n, err := writer.Write(req.data)
			req.result <- writeResult{bytesWritten: n, err: err}
		}
	}()

	// Main compression loop with parallel I/O
	for readRes := range readChan {
		if readRes.err != nil && readRes.err != io.EOF {
			close(writeChan)
			wg.Wait()
			return fmt.Errorf("parallel I/O read error: %w", readRes.err)
		}

		if len(readRes.data) > 0 {
			// Send write request
			resultChan := make(chan writeResult, 1)
			writeChan <- writeRequest{data: readRes.data, result: resultChan}

			// Wait for write to complete
			writeRes := <-resultChan
			if writeRes.err != nil {
				close(writeChan)
				wg.Wait()
				return fmt.Errorf("parallel I/O write error: %w", writeRes.err)
			}

			written += int64(writeRes.bytesWritten)
			if cfg.Progress {
				reporter.Update(written, totalSize)
			}
		}

		if readRes.err == io.EOF {
			break
		}
	}

	close(writeChan)
	wg.Wait()

	// Note: Finish is called by the parent function to include compression ratio
	return nil
}
