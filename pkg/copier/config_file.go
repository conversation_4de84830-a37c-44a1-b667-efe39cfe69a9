package copier

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

// ConfigProfile represents a saved configuration profile
type ConfigProfile struct {
	Name             string `json:"name"`
	Description      string `json:"description"`
	CompressionType  string `json:"compression_type"`
	CompressionLevel string `json:"compression_level"`
	BlockSize        int    `json:"block_size"`
	Verify           bool   `json:"verify"`
	Sparse           bool   `json:"sparse"`
	SkipBadSectors   bool   `json:"skip_bad_sectors"`
	UseCopyOffload   bool   `json:"use_copy_offload"`
	PreserveAttributes bool `json:"preserve_attributes"`
	Checksum         string `json:"checksum"`
}

// ConfigFile represents the entire configuration file
type ConfigFile struct {
	Version  string          `json:"version"`
	Profiles []ConfigProfile `json:"profiles"`
}

// getConfigDir returns the directory where config files should be stored
func getConfigDir() (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get user home directory: %w", err)
	}
	
	configDir := filepath.Join(homeDir, ".bella")
	
	// Create config directory if it doesn't exist
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create config directory: %w", err)
	}
	
	return configDir, nil
}

// getConfigFilePath returns the full path to the config file
func getConfigFilePath() (string, error) {
	configDir, err := getConfigDir()
	if err != nil {
		return "", err
	}
	return filepath.Join(configDir, "config.json"), nil
}

// LoadConfigFile loads the configuration file from disk
func LoadConfigFile() (*ConfigFile, error) {
	configPath, err := getConfigFilePath()
	if err != nil {
		return nil, err
	}
	
	// If config file doesn't exist, return empty config
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return &ConfigFile{
			Version:  "1.0",
			Profiles: []ConfigProfile{},
		}, nil
	}
	
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}
	
	var config ConfigFile
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}
	
	return &config, nil
}

// SaveConfigFile saves the configuration file to disk
func (cf *ConfigFile) SaveConfigFile() error {
	configPath, err := getConfigFilePath()
	if err != nil {
		return err
	}
	
	data, err := json.MarshalIndent(cf, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}
	
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}
	
	return nil
}

// AddProfile adds a new profile to the configuration
func (cf *ConfigFile) AddProfile(profile ConfigProfile) error {
	// Check if profile name already exists
	for _, p := range cf.Profiles {
		if p.Name == profile.Name {
			return fmt.Errorf("profile '%s' already exists", profile.Name)
		}
	}
	
	cf.Profiles = append(cf.Profiles, profile)
	return nil
}

// GetProfile retrieves a profile by name
func (cf *ConfigFile) GetProfile(name string) (*ConfigProfile, error) {
	for _, p := range cf.Profiles {
		if p.Name == name {
			return &p, nil
		}
	}
	return nil, fmt.Errorf("profile '%s' not found", name)
}

// RemoveProfile removes a profile by name
func (cf *ConfigFile) RemoveProfile(name string) error {
	for i, p := range cf.Profiles {
		if p.Name == name {
			cf.Profiles = append(cf.Profiles[:i], cf.Profiles[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("profile '%s' not found", name)
}

// ListProfiles returns a list of all profile names
func (cf *ConfigFile) ListProfiles() []string {
	names := make([]string, len(cf.Profiles))
	for i, p := range cf.Profiles {
		names[i] = p.Name
	}
	return names
}

// ConfigToProfile converts a Config to a ConfigProfile
func ConfigToProfile(cfg *Config, name, description string) ConfigProfile {
	return ConfigProfile{
		Name:               name,
		Description:        description,
		CompressionType:    cfg.CompressionType,
		CompressionLevel:   cfg.CompressionLevel,
		BlockSize:          cfg.BlockSize,
		Verify:             cfg.Verify,
		Sparse:             cfg.Sparse,
		SkipBadSectors:     cfg.SkipBadSectors,
		UseCopyOffload:     cfg.UseCopyOffload,
		PreserveAttributes: cfg.PreserveAttributes,
		Checksum:           cfg.Checksum,
	}
}

// ApplyProfile applies a profile to a Config
func (profile *ConfigProfile) ApplyProfile(cfg *Config) {
	cfg.CompressionType = profile.CompressionType
	cfg.CompressionLevel = profile.CompressionLevel
	cfg.BlockSize = profile.BlockSize
	cfg.Verify = profile.Verify
	cfg.Sparse = profile.Sparse
	cfg.SkipBadSectors = profile.SkipBadSectors
	cfg.UseCopyOffload = profile.UseCopyOffload
	cfg.PreserveAttributes = profile.PreserveAttributes
	cfg.Checksum = profile.Checksum
}

// CreateDefaultProfiles creates some default configuration profiles
func CreateDefaultProfiles() (*ConfigFile, error) {
	config := &ConfigFile{
		Version:  "1.0",
		Profiles: []ConfigProfile{},
	}
	
	// Fast compression profile
	fastProfile := ConfigProfile{
		Name:               "fast",
		Description:        "Fast compression with good performance",
		CompressionType:    "tar.gz",
		CompressionLevel:   "Good",
		BlockSize:          4 * 1024 * 1024,
		Verify:             false,
		Sparse:             false,
		SkipBadSectors:     false,
		UseCopyOffload:     true,
		PreserveAttributes: true,
		Checksum:           "",
	}
	
	// Best compression profile
	bestProfile := ConfigProfile{
		Name:               "best",
		Description:        "Best compression ratio (slower)",
		CompressionType:    "tar.xz",
		CompressionLevel:   "Best",
		BlockSize:          1 * 1024 * 1024,
		Verify:             true,
		Sparse:             false,
		SkipBadSectors:     false,
		UseCopyOffload:     true,
		PreserveAttributes: true,
		Checksum:           "sha256",
	}
	
	// Backup profile
	backupProfile := ConfigProfile{
		Name:               "backup",
		Description:        "Reliable backup with verification",
		CompressionType:    "tar.gz",
		CompressionLevel:   "Better",
		BlockSize:          2 * 1024 * 1024,
		Verify:             true,
		Sparse:             true,
		SkipBadSectors:     true,
		UseCopyOffload:     true,
		PreserveAttributes: true,
		Checksum:           "sha256",
	}
	
	config.Profiles = []ConfigProfile{fastProfile, bestProfile, backupProfile}
	return config, nil
}
