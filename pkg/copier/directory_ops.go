package copier

import (
	"archive/tar"
	"archive/zip"
	"compress/bzip2"
	"compress/gzip"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"

	"bella/pkg/progress"

	bzip2Writer "github.com/dsnet/compress/bzip2"
	"github.com/klauspost/compress/zstd"
	"github.com/ulikunitz/xz"
)

// copyDirectory performs a sequential, recursive copy of a directory.
func copyDirectory(cfg *Config) error {
	log.Printf("Preparing to recursively copy directory %s to %s\n", cfg.Input, cfg.Output)

	type fileJob struct {
		srcPath  string
		destPath string
		size     int64
	}
	var fileJobs []fileJob
	var totalSize int64

	err := filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err != nil {
			return err
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			return err
		}
		destPath := filepath.Join(cfg.Output, relPath)

		if d.<PERSON>() {
			if err := os.MkdirAll(destPath, 0755); err != nil {
				return err
			}
			if cfg.PreserveAttributes {
				if err := applyMetadata(path, destPath); err != nil {
					log.Printf("Warning: failed to apply metadata to directory '%s': %v", destPath, err)
				}
			}
			return nil
		}

		info, err := d.Info()
		if err != nil {
			return err
		}
		job := fileJob{srcPath: path, destPath: destPath, size: info.Size()}
		fileJobs = append(fileJobs, job)
		totalSize += info.Size()
		return nil
	})
	if err != nil {
		return fmt.Errorf("failed to scan source directory: %w", err)
	}

	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("copying directory", cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	var copiedSize int64
	for _, job := range fileJobs {
		fileCfg := *cfg
		fileCfg.Input = job.srcPath
		fileCfg.Output = job.destPath
		fileCfg.Progress = false
		fileCfg.isRecursive = true
		fileCfg.Append = false

		if err := copyFileSingleStage(&fileCfg); err != nil {
			return fmt.Errorf("failed to copy file %s: %w", job.srcPath, err)
		}

		copiedSize += job.size
		if cfg.Progress && reporter != nil {
			reporter.Update(copiedSize, totalSize)
		}
	}

	if cfg.Progress && reporter != nil {
		reporter.Update(totalSize, totalSize)
	}
	log.Println("Directory copy stage completed.")

	if cfg.Verify {
		log.Println("Starting directory verification stage.")
		if cfg.Progress && reporter != nil {
			reporter.SetStage("verifying directory")
			reporter.Update(0, totalSize)
		}

		var verifiedSize int64
		for _, job := range fileJobs {
			verifyCfg := *cfg
			verifyCfg.Input = job.srcPath
			verifyCfg.Output = job.destPath
			verifyCfg.Progress = false

			if err := doVerify(&verifyCfg); err != nil {
				return fmt.Errorf("verification failed for file '%s': %w", job.destPath, err)
			}
			verifiedSize += job.size
			if cfg.Progress && reporter != nil {
				reporter.Update(verifiedSize, totalSize)
			}
		}

		if cfg.Progress && reporter != nil {
			reporter.Finish(verifiedSize)
		}
		log.Println("Directory verification stage completed successfully.")
	} else {
		if cfg.Progress && reporter != nil {
			reporter.Finish(totalSize)
		}
	}

	return nil
}

// CreateArchive determines which archive format to use and creates it.
func CreateArchive(cfg *Config) error {
	// Ensure the output path has the correct compression extension
	cfg.Output = ensureCompressionExtension(cfg.Output, cfg.CompressionType)

	switch cfg.CompressionType {
	case "tar.gz":
		return createTarGz(cfg)
	case "tar.xz":
		return createTarXz(cfg)
	case "tar.bz2":
		return createTarBz2(cfg)
	case "tar.zst":
		return createTarZst(cfg)
	case "zip":
		return createZip(cfg)
	default:
		return fmt.Errorf("unsupported archive type for directory compression: %s", cfg.CompressionType)
	}
}

// createTarGz creates a .tar.gz archive from a source directory.
func createTarGz(cfg *Config) error {
	outFile, err := os.Create(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to create archive file: %w", err)
	}
	defer outFile.Close()

	level, err := mapCompressionLevel(cfg.CompressionLevel, "tar.gz")
	if err != nil {
		return err
	}
	gzw, err := gzip.NewWriterLevel(outFile, level)
	if err != nil {
		return fmt.Errorf("failed to create gzip writer: %w", err)
	}
	defer gzw.Close()
	tw := tar.NewWriter(gzw)
	defer tw.Close()

	var totalSize int64
	var filePaths []string
	filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err == nil && !d.IsDir() {
			info, _ := d.Info()
			totalSize += info.Size()
			filePaths = append(filePaths, path)
		}
		return nil
	})

	reporter := progress.NewReporter("Archiving (tar.gz)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize) // Show progress based on uncompressed data size
	}

	var writtenSize int64
	for _, path := range filePaths {
		info, err := os.Stat(path)
		if err != nil {
			continue
		}
		header, err := tar.FileInfoHeader(info, info.Name())
		if err != nil {
			continue
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			continue
		}
		header.Name = relPath
		if err := tw.WriteHeader(header); err != nil {
			return err
		}
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		n, err := io.Copy(tw, file)
		if err != nil {
			file.Close()
			return err
		}
		file.Close()
		writtenSize += n
		if cfg.Progress {
			reporter.Update(writtenSize, totalSize)
		}
	}

	if cfg.Progress {
		// Calculate compression ratio for tar.gz
		compressedSize, sizeErr := getFileSize(cfg.Output)
		if sizeErr == nil {
			reporter.FinishCompression(writtenSize, totalSize, compressedSize)
		} else {
			reporter.Finish(writtenSize)
		}
	}
	return nil
}

// createTarXz creates a .tar.xz archive from a source directory.
func createTarXz(cfg *Config) error {
	outFile, err := os.Create(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to create archive file: %w", err)
	}
	defer outFile.Close()

	// Use WriterConfig to support compression levels for xz
	config := xz.WriterConfig{}
	level, err := mapCompressionLevel(cfg.CompressionLevel, "xz")
	if err != nil {
		return err
	}
	// Map compression level to xz properties
	if level >= 9 {
		config.DictCap = 64 * 1024 * 1024 // 64MB for best compression
	} else if level >= 7 {
		config.DictCap = 16 * 1024 * 1024 // 16MB for better compression
	} else {
		config.DictCap = 8 * 1024 * 1024 // 8MB for good compression
	}
	xzw, err := config.NewWriter(outFile)
	if err != nil {
		return fmt.Errorf("failed to create xz writer: %w", err)
	}
	defer xzw.Close()
	tw := tar.NewWriter(xzw)
	defer tw.Close()

	var totalSize int64
	var filePaths []string
	filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err == nil && !d.IsDir() {
			info, _ := d.Info()
			totalSize += info.Size()
			filePaths = append(filePaths, path)
		}
		return nil
	})

	reporter := progress.NewReporter("Archiving (tar.xz)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize) // Show progress based on uncompressed data size
	}

	var writtenSize int64
	for _, path := range filePaths {
		info, err := os.Stat(path)
		if err != nil {
			continue
		}
		header, err := tar.FileInfoHeader(info, info.Name())
		if err != nil {
			continue
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			continue
		}
		header.Name = relPath
		if err := tw.WriteHeader(header); err != nil {
			return err
		}
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		n, err := io.Copy(tw, file)
		if err != nil {
			file.Close()
			return err
		}
		file.Close()
		writtenSize += n
		if cfg.Progress {
			reporter.Update(writtenSize, totalSize)
		}
	}

	if cfg.Progress {
		// Calculate compression ratio for tar.xz
		compressedSize, sizeErr := getFileSize(cfg.Output)
		if sizeErr == nil {
			reporter.FinishCompression(writtenSize, totalSize, compressedSize)
		} else {
			reporter.Finish(writtenSize)
		}
	}
	return nil
}

// createTarBz2 creates a .tar.bz2 archive from a source directory.
func createTarBz2(cfg *Config) error {
	outFile, err := os.Create(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to create archive file: %w", err)
	}
	defer outFile.Close()

	// Use bzip2 writer with compression level support
	level, err := mapCompressionLevel(cfg.CompressionLevel, "bzip2")
	if err != nil {
		return err
	}
	config := &bzip2Writer.WriterConfig{Level: level}
	bw, err := bzip2Writer.NewWriter(outFile, config)
	if err != nil {
		return fmt.Errorf("failed to create bzip2 writer: %w", err)
	}
	defer bw.Close()
	tw := tar.NewWriter(bw)
	defer tw.Close()

	var totalSize int64
	var filePaths []string
	filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err == nil && !d.IsDir() {
			info, _ := d.Info()
			totalSize += info.Size()
			filePaths = append(filePaths, path)
		}
		return nil
	})

	reporter := progress.NewReporter("Archiving (tar.bz2)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize) // Show progress based on uncompressed data size
	}

	var writtenSize int64
	for _, path := range filePaths {
		info, err := os.Stat(path)
		if err != nil {
			continue
		}
		header, err := tar.FileInfoHeader(info, info.Name())
		if err != nil {
			continue
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			continue
		}
		header.Name = relPath
		if err := tw.WriteHeader(header); err != nil {
			return err
		}
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		n, err := io.Copy(tw, file)
		if err != nil {
			file.Close()
			return err
		}
		file.Close()
		writtenSize += n
		if cfg.Progress {
			reporter.Update(writtenSize, totalSize)
		}
	}

	if cfg.Progress {
		// Calculate compression ratio for tar.bz2
		compressedSize, sizeErr := getFileSize(cfg.Output)
		if sizeErr == nil {
			reporter.FinishCompression(writtenSize, totalSize, compressedSize)
		} else {
			reporter.Finish(writtenSize)
		}
	}
	return nil
}

// createTarZst creates a .tar.zst archive from a source directory.
func createTarZst(cfg *Config) error {
	outFile, err := os.Create(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to create archive file: %w", err)
	}
	defer outFile.Close()

	// Use zstd writer with compression level support
	level, err := mapCompressionLevel(cfg.CompressionLevel, "zstd")
	if err != nil {
		return err
	}
	// Map compression level to zstd level
	var zstdLevel zstd.EncoderLevel
	if level >= 9 {
		zstdLevel = zstd.SpeedBestCompression
	} else if level >= 7 {
		zstdLevel = zstd.SpeedBetterCompression
	} else {
		zstdLevel = zstd.SpeedDefault
	}
	zw, err := zstd.NewWriter(outFile, zstd.WithEncoderLevel(zstdLevel))
	if err != nil {
		return fmt.Errorf("failed to create zstd writer: %w", err)
	}
	defer zw.Close()
	tw := tar.NewWriter(zw)
	defer tw.Close()

	var totalSize int64
	var filePaths []string
	filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err == nil && !d.IsDir() {
			info, _ := d.Info()
			totalSize += info.Size()
			filePaths = append(filePaths, path)
		}
		return nil
	})

	reporter := progress.NewReporter("Archiving (tar.zst)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize) // Show progress based on uncompressed data size
	}

	var writtenSize int64
	for _, path := range filePaths {
		info, err := os.Stat(path)
		if err != nil {
			continue
		}
		header, err := tar.FileInfoHeader(info, info.Name())
		if err != nil {
			continue
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			continue
		}
		header.Name = relPath
		if err := tw.WriteHeader(header); err != nil {
			return err
		}
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		n, err := io.Copy(tw, file)
		if err != nil {
			file.Close()
			return err
		}
		file.Close()
		writtenSize += n
		if cfg.Progress {
			reporter.Update(writtenSize, totalSize)
		}
	}

	if cfg.Progress {
		// Calculate compression ratio for tar.zst
		compressedSize, sizeErr := getFileSize(cfg.Output)
		if sizeErr == nil {
			reporter.FinishCompression(writtenSize, totalSize, compressedSize)
		} else {
			reporter.Finish(writtenSize)
		}
	}
	return nil
}

// createZip creates a .zip archive from a source directory.
func createZip(cfg *Config) error {
	outFile, err := os.Create(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to create archive file: %w", err)
	}
	defer outFile.Close()
	zw := zip.NewWriter(outFile)
	defer zw.Close()

	// Note: Password protection for ZIP files would require a third-party library
	// such as github.com/alexmullins/zip or similar, as the standard library
	// does not support encrypted ZIP files
	if cfg.Password != "" {
		fmt.Printf("Warning: Password protection is not yet implemented. Archive will be created without encryption.\n")
		fmt.Printf("To implement password protection, a third-party library like github.com/alexmullins/zip would be needed.\n")
	}

	var totalSize int64
	var filePaths []string
	filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err == nil && !d.IsDir() {
			info, _ := d.Info()
			totalSize += info.Size()
			filePaths = append(filePaths, path)
		}
		return nil
	})

	reporter := progress.NewReporter("Archiving (zip)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize)
	}

	var writtenSize int64
	for _, path := range filePaths {
		info, err := os.Stat(path)
		if err != nil {
			continue
		}
		header, err := zip.FileInfoHeader(info)
		if err != nil {
			continue
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			continue
		}
		header.Name = relPath
		header.Method = zip.Deflate
		writer, err := zw.CreateHeader(header)
		if err != nil {
			return err
		}
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		n, err := io.Copy(writer, file)
		if err != nil {
			file.Close()
			return err
		}
		file.Close()
		writtenSize += n
		if cfg.Progress {
			reporter.Update(writtenSize, totalSize)
		}
	}

	if cfg.Progress {
		// Calculate compression ratio for zip
		compressedSize, sizeErr := getFileSize(cfg.Output)
		if sizeErr == nil {
			reporter.FinishCompression(writtenSize, totalSize, compressedSize)
		} else {
			reporter.Finish(writtenSize)
		}
	}
	return nil
}

// ExtractArchive determines which archive format to extract and extracts it.
func ExtractArchive(cfg *Config) error {
	// Auto-detect archive type from file extension if not explicitly set
	archiveType := cfg.CompressionType
	if cfg.Compression == "auto" {
		if strings.HasSuffix(cfg.Input, ".tar.gz") {
			archiveType = "tar.gz"
		} else if strings.HasSuffix(cfg.Input, ".tar.xz") {
			archiveType = "tar.xz"
		} else if strings.HasSuffix(cfg.Input, ".tar.bz2") {
			archiveType = "tar.bz2"
		} else if strings.HasSuffix(cfg.Input, ".tar.zst") {
			archiveType = "tar.zst"
		} else if strings.HasSuffix(cfg.Input, ".zip") {
			archiveType = "zip"
		}
	}

	switch archiveType {
	case "tar.gz":
		return extractTarGz(cfg)
	case "tar.xz":
		return extractTarXz(cfg)
	case "tar.bz2":
		return extractTarBz2(cfg)
	case "tar.zst":
		return extractTarZst(cfg)
	case "zip":
		return extractZip(cfg)
	default:
		return fmt.Errorf("unsupported archive type for extraction: %s", archiveType)
	}
}

// extractZip extracts a .zip archive to a destination directory.
func extractZip(cfg *Config) error {
	// Note: Password protection for ZIP files would require a third-party library
	// such as github.com/alexmullins/zip or similar, as the standard library
	// does not support encrypted ZIP files
	if cfg.Password != "" {
		return fmt.Errorf("password-protected ZIP extraction is not yet implemented - requires third-party library")
	}

	reader, err := zip.OpenReader(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open zip file (archive may be corrupted): %w", err)
	}
	defer reader.Close()

	// Ensure output directory exists
	if err := os.MkdirAll(cfg.Output, 0755); err != nil {
		if os.IsPermission(err) {
			return fmt.Errorf("permission denied creating output directory '%s': %w", cfg.Output, err)
		}
		return fmt.Errorf("failed to create output directory '%s': %w", cfg.Output, err)
	}

	var totalSize int64
	for _, file := range reader.File {
		totalSize += int64(file.UncompressedSize64)
	}

	reporter := progress.NewReporter("Extracting (zip)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize)
	}

	var extractedSize int64
	for _, file := range reader.File {
		path := filepath.Join(cfg.Output, file.Name)

		// Security check: ensure the file path is within the output directory
		if !strings.HasPrefix(path, filepath.Clean(cfg.Output)+string(os.PathSeparator)) {
			return fmt.Errorf("invalid file path in archive: %s", file.Name)
		}

		if file.FileInfo().IsDir() {
			os.MkdirAll(path, file.FileInfo().Mode())
			continue
		}

		// Create the directories for this file
		if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
			return fmt.Errorf("failed to create directory for file %s: %w", file.Name, err)
		}

		// Check if file already exists and prompt for action
		if _, err := os.Stat(path); err == nil {
			action, promptErr := promptForFileAction(path, cfg.IsUIMode, false)
			if promptErr != nil {
				return promptErr
			}
			switch action {
			case "cancel":
				return fmt.Errorf("extraction cancelled by user")
			case "append":
				// For extraction, append doesn't make much sense, treat as overwrite
				fallthrough
			case "overwrite":
				// Continue with extraction
			default:
				return fmt.Errorf("invalid action: %s", action)
			}
		}

		fileReader, err := file.Open()
		if err != nil {
			return fmt.Errorf("failed to open file in archive: %w", err)
		}

		outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.FileInfo().Mode())
		if err != nil {
			fileReader.Close()
			return fmt.Errorf("failed to create output file: %w", err)
		}

		n, err := io.Copy(outFile, fileReader)
		fileReader.Close()
		outFile.Close()

		if err != nil {
			return fmt.Errorf("failed to extract file %s: %w", file.Name, err)
		}

		extractedSize += n
		if cfg.Progress {
			reporter.Update(extractedSize, totalSize)
		}
	}

	if cfg.Progress {
		reporter.Finish(extractedSize)
	}
	return nil
}

// extractTarGz extracts a .tar.gz archive to a destination directory.
func extractTarGz(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.gz file: %w", err)
	}
	defer file.Close()

	gzr, err := gzip.NewReader(file)
	if err != nil {
		return fmt.Errorf("failed to create gzip reader (archive may be corrupted): %w", err)
	}
	defer gzr.Close()

	return extractTar(gzr, cfg, "tar.gz")
}

// extractTarXz extracts a .tar.xz archive to a destination directory.
func extractTarXz(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.xz file: %w", err)
	}
	defer file.Close()

	xzr, err := xz.NewReader(file)
	if err != nil {
		return fmt.Errorf("failed to create xz reader (archive may be corrupted): %w", err)
	}

	return extractTar(xzr, cfg, "tar.xz")
}

// extractTarBz2 extracts a .tar.bz2 archive to a destination directory.
func extractTarBz2(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.bz2 file: %w", err)
	}
	defer file.Close()

	bzr := bzip2.NewReader(file)

	return extractTar(bzr, cfg, "tar.bz2")
}

// extractTarZst extracts a .tar.zst archive to a destination directory.
func extractTarZst(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.zst file: %w", err)
	}
	defer file.Close()

	zr, err := zstd.NewReader(file)
	if err != nil {
		return fmt.Errorf("failed to create zstd reader: %w", err)
	}
	defer zr.Close()

	return extractTar(zr, cfg, "tar.zst")
}

// BrowseArchive lists the contents of an archive without extracting it.
func BrowseArchive(cfg *Config) error {
	// Auto-detect archive type from file extension if not explicitly set
	archiveType := cfg.CompressionType
	if cfg.Compression == "auto" || cfg.CompressionType == "" {
		lowerInput := strings.ToLower(cfg.Input)
		if strings.HasSuffix(lowerInput, ".tar.gz") || strings.HasSuffix(lowerInput, ".tgz") {
			archiveType = "tar.gz"
		} else if strings.HasSuffix(lowerInput, ".tar.xz") || strings.HasSuffix(lowerInput, ".txz") {
			archiveType = "tar.xz"
		} else if strings.HasSuffix(lowerInput, ".tar.bz2") {
			archiveType = "tar.bz2"
		} else if strings.HasSuffix(lowerInput, ".tar.zst") {
			archiveType = "tar.zst"
		} else if strings.HasSuffix(lowerInput, ".zip") {
			archiveType = "zip"
		} else {
			return fmt.Errorf("unable to auto-detect archive type from file extension: %s", cfg.Input)
		}
	}

	switch archiveType {
	case "tar.gz":
		return browseTarGz(cfg)
	case "tar.xz":
		return browseTarXz(cfg)
	case "tar.bz2":
		return browseTarBz2(cfg)
	case "tar.zst":
		return browseTarZst(cfg)
	case "zip":
		return browseZip(cfg)
	default:
		return fmt.Errorf("unsupported archive type for browsing: %s", archiveType)
	}
}

// browseTarGz lists the contents of a .tar.gz archive.
func browseTarGz(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.gz file: %w", err)
	}
	defer file.Close()

	gr, err := gzip.NewReader(file)
	if err != nil {
		return fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer gr.Close()

	return browseTar(gr, cfg, "tar.gz")
}

// browseTarXz lists the contents of a .tar.xz archive.
func browseTarXz(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.xz file: %w", err)
	}
	defer file.Close()

	xzr, err := xz.NewReader(file)
	if err != nil {
		return fmt.Errorf("failed to create xz reader: %w", err)
	}

	return browseTar(xzr, cfg, "tar.xz")
}

// browseTarBz2 lists the contents of a .tar.bz2 archive.
func browseTarBz2(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.bz2 file: %w", err)
	}
	defer file.Close()

	bzr := bzip2.NewReader(file)

	return browseTar(bzr, cfg, "tar.bz2")
}

// browseTarZst lists the contents of a .tar.zst archive.
func browseTarZst(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.zst file: %w", err)
	}
	defer file.Close()

	zr, err := zstd.NewReader(file)
	if err != nil {
		return fmt.Errorf("failed to create zstd reader: %w", err)
	}
	defer zr.Close()

	return browseTar(zr, cfg, "tar.zst")
}

// browseTar lists the contents of a tar archive from the given reader.
func browseTar(reader io.Reader, cfg *Config, archiveType string) error {
	tr := tar.NewReader(reader)

	fmt.Printf("Contents of %s archive: %s\n", archiveType, cfg.Input)
	fmt.Printf("%-50s %10s %20s %s\n", "Name", "Size", "Mode", "Modified")
	fmt.Printf("%s\n", strings.Repeat("-", 90))

	var totalSize int64
	var fileCount int

	for {
		header, err := tr.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("failed to read tar header: %w", err)
		}

		// Format file mode
		mode := header.FileInfo().Mode().String()

		// Format modification time
		modTime := header.ModTime.Format("2006-01-02 15:04:05")

		// Format size
		sizeStr := formatSize(header.Size)

		fmt.Printf("%-50s %10s %20s %s\n", header.Name, sizeStr, mode, modTime)

		if header.Typeflag == tar.TypeReg {
			totalSize += header.Size
			fileCount++
		}
	}

	fmt.Printf("%s\n", strings.Repeat("-", 90))
	fmt.Printf("Total: %d files, %s\n", fileCount, formatSize(totalSize))
	return nil
}

// browseZip lists the contents of a .zip archive.
func browseZip(cfg *Config) error {
	r, err := zip.OpenReader(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open zip file: %w", err)
	}
	defer r.Close()

	fmt.Printf("Contents of zip archive: %s\n", cfg.Input)
	fmt.Printf("%-50s %10s %20s %s\n", "Name", "Size", "Mode", "Modified")
	fmt.Printf("%s\n", strings.Repeat("-", 90))

	var totalSize int64
	var fileCount int

	for _, f := range r.File {
		// Format file mode
		mode := f.FileInfo().Mode().String()

		// Format modification time
		modTime := f.FileInfo().ModTime().Format("2006-01-02 15:04:05")

		// Format size
		sizeStr := formatSize(int64(f.UncompressedSize64))

		fmt.Printf("%-50s %10s %20s %s\n", f.Name, sizeStr, mode, modTime)

		if !f.FileInfo().IsDir() {
			totalSize += int64(f.UncompressedSize64)
			fileCount++
		}
	}

	fmt.Printf("%s\n", strings.Repeat("-", 90))
	fmt.Printf("Total: %d files, %s\n", fileCount, formatSize(totalSize))
	return nil
}

// formatSize formats a size in bytes to a human-readable string.
func formatSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// extractTar extracts a tar archive from the given reader.
func extractTar(reader io.Reader, cfg *Config, archiveType string) error {
	tr := tar.NewReader(reader)

	// Ensure output directory exists
	if err := os.MkdirAll(cfg.Output, 0755); err != nil {
		if os.IsPermission(err) {
			return fmt.Errorf("permission denied creating output directory '%s': %w", cfg.Output, err)
		}
		return fmt.Errorf("failed to create output directory '%s': %w", cfg.Output, err)
	}

	reporter := progress.NewReporter(fmt.Sprintf("Extracting (%s)", archiveType), cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, 0) // Total size unknown for tar files - will show bytes processed
	}

	var extractedSize int64
	var conflictAction string // Cache user's choice for multiple conflicts
	for {
		header, err := tr.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("failed to read tar header (archive may be corrupted): %w", err)
		}

		path := filepath.Join(cfg.Output, header.Name)

		// Security check: ensure the file path is within the output directory
		if !strings.HasPrefix(path, filepath.Clean(cfg.Output)+string(os.PathSeparator)) {
			return fmt.Errorf("invalid file path in archive: %s", header.Name)
		}

		switch header.Typeflag {
		case tar.TypeDir:
			if err := os.MkdirAll(path, os.FileMode(header.Mode)); err != nil {
				return fmt.Errorf("failed to create directory %s: %w", path, err)
			}
		case tar.TypeReg:
			// Create the directories for this file
			if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
				return fmt.Errorf("failed to create directory for file %s: %w", header.Name, err)
			}

			// Check if file already exists and prompt for action
			if _, err := os.Stat(path); err == nil {
				// Use cached action if user already decided for previous conflicts
				if conflictAction == "" {
					action, promptErr := promptForFileAction(path, cfg.IsUIMode, false)
					if promptErr != nil {
						return promptErr
					}
					conflictAction = action // Cache the decision
				}
				switch conflictAction {
				case "cancel":
					return fmt.Errorf("extraction cancelled by user")
				case "append":
					// For extraction, append doesn't make much sense, treat as overwrite
					fallthrough
				case "overwrite":
					// Continue with extraction
				default:
					return fmt.Errorf("invalid action: %s", conflictAction)
				}
			}

			outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.FileMode(header.Mode))
			if err != nil {
				if os.IsPermission(err) {
					return fmt.Errorf("permission denied creating file '%s': %w", path, err)
				}
				return fmt.Errorf("failed to create output file '%s': %w", path, err)
			}

			n, err := io.Copy(outFile, tr)
			outFile.Close()

			if err != nil {
				if os.IsPermission(err) {
					return fmt.Errorf("permission denied writing to file '%s': %w", path, err)
				}
				return fmt.Errorf("failed to extract file '%s': %w", header.Name, err)
			}

			extractedSize += n
			if cfg.Progress {
				// For tar files, show progress as bytes extracted (no total size available)
				reporter.Update(extractedSize, 0)
			}
		}
	}

	if cfg.Progress {
		reporter.Finish(extractedSize)
	}
	return nil
}

// SelectiveExtractArchive extracts only selected files from an archive
func SelectiveExtractArchive(cfg *Config) error {
	if len(cfg.SelectedFiles) == 0 {
		return fmt.Errorf("no files selected for extraction")
	}

	// Auto-detect archive type from file extension if not explicitly set
	archiveType := cfg.CompressionType
	if cfg.Compression == "auto" {
		if strings.HasSuffix(cfg.Input, ".tar.gz") {
			archiveType = "tar.gz"
		} else if strings.HasSuffix(cfg.Input, ".tar.xz") {
			archiveType = "tar.xz"
		} else if strings.HasSuffix(cfg.Input, ".tar.bz2") {
			archiveType = "tar.bz2"
		} else if strings.HasSuffix(cfg.Input, ".tar.zst") {
			archiveType = "tar.zst"
		} else if strings.HasSuffix(cfg.Input, ".zip") {
			archiveType = "zip"
		}
	}

	switch archiveType {
	case "tar.gz":
		return selectiveExtractTarGz(cfg)
	case "tar.xz":
		return selectiveExtractTarXz(cfg)
	case "tar.bz2":
		return selectiveExtractTarBz2(cfg)
	case "tar.zst":
		return selectiveExtractTarZst(cfg)
	case "zip":
		return selectiveExtractZip(cfg)
	default:
		return fmt.Errorf("unsupported archive type for selective extraction: %s", archiveType)
	}
}

// selectiveExtractTarGz extracts selected files from a .tar.gz archive
func selectiveExtractTarGz(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.gz file: %w", err)
	}
	defer file.Close()

	gzr, err := gzip.NewReader(file)
	if err != nil {
		return fmt.Errorf("failed to create gzip reader (archive may be corrupted): %w", err)
	}
	defer gzr.Close()

	return selectiveExtractTar(gzr, cfg, "tar.gz")
}

// selectiveExtractTarXz extracts selected files from a .tar.xz archive
func selectiveExtractTarXz(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.xz file: %w", err)
	}
	defer file.Close()

	xzr, err := xz.NewReader(file)
	if err != nil {
		return fmt.Errorf("failed to create xz reader (archive may be corrupted): %w", err)
	}

	return selectiveExtractTar(xzr, cfg, "tar.xz")
}

// selectiveExtractTarBz2 extracts selected files from a .tar.bz2 archive
func selectiveExtractTarBz2(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.bz2 file: %w", err)
	}
	defer file.Close()

	bzr := bzip2.NewReader(file)

	return selectiveExtractTar(bzr, cfg, "tar.bz2")
}

// selectiveExtractTarZst extracts selected files from a .tar.zst archive
func selectiveExtractTarZst(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.zst file: %w", err)
	}
	defer file.Close()

	zr, err := zstd.NewReader(file)
	if err != nil {
		return fmt.Errorf("failed to create zstd reader: %w", err)
	}
	defer zr.Close()

	return selectiveExtractTar(zr, cfg, "tar.zst")
}

// selectiveExtractTar extracts selected files from a tar archive
func selectiveExtractTar(reader io.Reader, cfg *Config, archiveType string) error {
	tr := tar.NewReader(reader)

	// Ensure output directory exists
	if err := os.MkdirAll(cfg.Output, 0755); err != nil {
		if os.IsPermission(err) {
			return fmt.Errorf("permission denied creating output directory '%s': %w", cfg.Output, err)
		}
		return fmt.Errorf("failed to create output directory '%s': %w", cfg.Output, err)
	}

	// Create a map for faster file lookup
	selectedFiles := make(map[string]bool)
	for _, file := range cfg.SelectedFiles {
		selectedFiles[file] = true
	}

	reporter := progress.NewReporter(fmt.Sprintf("Selective Extract (%s)", archiveType), cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, 0) // Total size unknown for tar files
	}

	var extractedSize int64
	var extractedCount int
	var conflictAction string // Cache user's choice for multiple conflicts

	for {
		header, err := tr.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("failed to read tar header (archive may be corrupted): %w", err)
		}

		// Check if this file is in our selection
		if !selectedFiles[header.Name] {
			continue // Skip files not in selection
		}

		path := filepath.Join(cfg.Output, header.Name)

		// Security check: ensure the file path is within the output directory
		if !strings.HasPrefix(path, filepath.Clean(cfg.Output)+string(os.PathSeparator)) {
			return fmt.Errorf("invalid file path in archive: %s", header.Name)
		}

		if header.Typeflag == tar.TypeDir {
			if err := os.MkdirAll(path, os.FileMode(header.Mode)); err != nil {
				return fmt.Errorf("failed to create directory %s: %w", header.Name, err)
			}
			continue
		}

		if header.Typeflag == tar.TypeReg {
			// Create the directories for this file
			if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
				return fmt.Errorf("failed to create directory for file %s: %w", header.Name, err)
			}

			// Handle file conflicts if in UI mode
			if cfg.IsUIMode && conflictAction != "overwrite_all" && conflictAction != "skip_all" {
				if _, err := os.Stat(path); err == nil {
					if conflictAction == "" {
						// File exists, need to prompt user
						return fmt.Errorf("file conflict detected: %s already exists (use CLI mode for automatic handling)", path)
					}
				}
			}

			outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.FileMode(header.Mode))
			if err != nil {
				if os.IsPermission(err) {
					return fmt.Errorf("permission denied creating file '%s': %w", path, err)
				}
				return fmt.Errorf("failed to create output file '%s': %w", path, err)
			}

			n, err := io.Copy(outFile, tr)
			outFile.Close()

			if err != nil {
				if os.IsPermission(err) {
					return fmt.Errorf("permission denied writing to file '%s': %w", path, err)
				}
				return fmt.Errorf("failed to extract file '%s': %w", header.Name, err)
			}

			extractedSize += n
			extractedCount++
			if cfg.Progress {
				reporter.Update(extractedSize, 0)
			}
		}
	}

	if cfg.Progress {
		reporter.Finish(extractedSize)
	}

	fmt.Printf("Selective extraction completed: %d files extracted, %s total\n",
		extractedCount, formatSize(extractedSize))
	return nil
}

// selectiveExtractZip extracts selected files from a .zip archive
func selectiveExtractZip(cfg *Config) error {
	reader, err := zip.OpenReader(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open zip file (archive may be corrupted): %w", err)
	}
	defer reader.Close()

	// Ensure output directory exists
	if err := os.MkdirAll(cfg.Output, 0755); err != nil {
		if os.IsPermission(err) {
			return fmt.Errorf("permission denied creating output directory '%s': %w", cfg.Output, err)
		}
		return fmt.Errorf("failed to create output directory '%s': %w", cfg.Output, err)
	}

	// Create a map for faster file lookup
	selectedFiles := make(map[string]bool)
	for _, file := range cfg.SelectedFiles {
		selectedFiles[file] = true
	}

	// Calculate total size of selected files
	var totalSize int64
	var selectedFileList []*zip.File
	for _, file := range reader.File {
		if selectedFiles[file.Name] {
			totalSize += int64(file.UncompressedSize64)
			selectedFileList = append(selectedFileList, file)
		}
	}

	reporter := progress.NewReporter("Selective Extract (zip)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize)
	}

	var extractedSize int64
	var extractedCount int
	for _, file := range selectedFileList {
		path := filepath.Join(cfg.Output, file.Name)

		// Security check: ensure the file path is within the output directory
		if !strings.HasPrefix(path, filepath.Clean(cfg.Output)+string(os.PathSeparator)) {
			return fmt.Errorf("invalid file path in archive: %s", file.Name)
		}

		if file.FileInfo().IsDir() {
			os.MkdirAll(path, file.FileInfo().Mode())
			continue
		}

		// Create the directories for this file
		if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
			return fmt.Errorf("failed to create directory for file %s: %w", file.Name, err)
		}

		// Handle file conflicts if in UI mode
		if cfg.IsUIMode {
			if _, err := os.Stat(path); err == nil {
				return fmt.Errorf("file conflict detected: %s already exists (use CLI mode for automatic handling)", path)
			}
		}

		rc, err := file.Open()
		if err != nil {
			return fmt.Errorf("failed to open file in archive: %s: %w", file.Name, err)
		}

		outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.FileInfo().Mode())
		if err != nil {
			rc.Close()
			if os.IsPermission(err) {
				return fmt.Errorf("permission denied creating file '%s': %w", path, err)
			}
			return fmt.Errorf("failed to create output file '%s': %w", path, err)
		}

		n, err := io.Copy(outFile, rc)
		rc.Close()
		outFile.Close()

		if err != nil {
			if os.IsPermission(err) {
				return fmt.Errorf("permission denied writing to file '%s': %w", path, err)
			}
			return fmt.Errorf("failed to extract file '%s': %w", file.Name, err)
		}

		extractedSize += n
		extractedCount++
		if cfg.Progress {
			reporter.Update(extractedSize, totalSize)
		}
	}

	if cfg.Progress {
		reporter.Finish(extractedSize)
	}

	fmt.Printf("Selective extraction completed: %d files extracted, %s total\n",
		extractedCount, formatSize(extractedSize))
	return nil
}
