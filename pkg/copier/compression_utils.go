package copier

import (
	"compress/gzip"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// mapCompressionLevel maps user-friendly names to compression constants.
func mapCompressionLevel(level string, compType string) (int, error) {
	var l int
	switch strings.ToLower(level) {
	case "good":
		l = 6
	case "better":
		l = 7
	case "best":
		l = 9
	default:
		l = 6
	}

	if compType == "gzip" {
		if l > gzip.BestCompression {
			l = gzip.BestCompression
		}
	}
	return l, nil
}

// ensureCompressionExtension ensures the output path has the correct extension for the compression type
func ensureCompressionExtension(outputPath, compressionType string) string {
	var expectedExt string

	switch compressionType {
	case "gzip":
		expectedExt = ".gz"
	case "xz":
		expectedExt = ".xz"
	case "bzip2":
		expectedExt = ".bz2"
	case "zstd":
		expectedExt = ".zst"
	case "zip":
		expectedExt = ".zip"
	case "tar.gz":
		expectedExt = ".tar.gz"
	case "tar.xz":
		expectedExt = ".tar.xz"
	case "tar.bz2":
		expectedExt = ".tar.bz2"
	case "tar.zst":
		expectedExt = ".tar.zst"
	default:
		return outputPath // No extension needed for unknown types
	}

	// Check if the output already has the correct extension
	lowerPath := strings.ToLower(outputPath)
	if strings.HasSuffix(lowerPath, expectedExt) {
		return outputPath
	}

	// Also check for shorthand extensions
	if compressionType == "tar.gz" && strings.HasSuffix(lowerPath, ".tgz") {
		return outputPath
	}
	if compressionType == "tar.xz" && strings.HasSuffix(lowerPath, ".txz") {
		return outputPath
	}

	// Check if it has any compression extension and replace it
	// (lowerPath already defined above)
	if strings.HasSuffix(lowerPath, ".gz") ||
		strings.HasSuffix(lowerPath, ".xz") ||
		strings.HasSuffix(lowerPath, ".bz2") ||
		strings.HasSuffix(lowerPath, ".zst") ||
		strings.HasSuffix(lowerPath, ".zip") ||
		strings.HasSuffix(lowerPath, ".tar.gz") ||
		strings.HasSuffix(lowerPath, ".tar.xz") ||
		strings.HasSuffix(lowerPath, ".tar.bz2") ||
		strings.HasSuffix(lowerPath, ".tar.zst") {
		// Remove existing compression extension and add the correct one
		if strings.HasSuffix(lowerPath, ".tar.gz") {
			outputPath = outputPath[:len(outputPath)-7]
		} else if strings.HasSuffix(lowerPath, ".tar.xz") {
			outputPath = outputPath[:len(outputPath)-7]
		} else if strings.HasSuffix(lowerPath, ".tar.bz2") {
			outputPath = outputPath[:len(outputPath)-8]
		} else if strings.HasSuffix(lowerPath, ".tar.zst") {
			outputPath = outputPath[:len(outputPath)-8]
		} else if strings.HasSuffix(lowerPath, ".gz") {
			outputPath = outputPath[:len(outputPath)-3]
		} else if strings.HasSuffix(lowerPath, ".xz") {
			outputPath = outputPath[:len(outputPath)-3]
		} else if strings.HasSuffix(lowerPath, ".bz2") {
			outputPath = outputPath[:len(outputPath)-4]
		} else if strings.HasSuffix(lowerPath, ".zst") {
			outputPath = outputPath[:len(outputPath)-4]
		} else if strings.HasSuffix(lowerPath, ".zip") {
			outputPath = outputPath[:len(outputPath)-4]
		}
	}

	return outputPath + expectedExt
}

// appendTempFileToFinal appends the content of a temporary file to the final output file
func appendTempFileToFinal(tempPath, finalPath string) error {
	tempFile, err := os.Open(tempPath)
	if err != nil {
		return fmt.Errorf("failed to open temporary file for append: %w", err)
	}
	defer tempFile.Close()

	finalFile, err := os.OpenFile(finalPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("failed to open final file for append: %w", err)
	}
	defer finalFile.Close()

	_, err = io.Copy(finalFile, tempFile)
	if err != nil {
		return fmt.Errorf("failed to append temp file content: %w", err)
	}

	return finalFile.Sync()
}

// getFileSize returns the size of a file in bytes
func getFileSize(filePath string) (int64, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0, err
	}
	return info.Size(), nil
}

// CompressionPreview contains estimated compression information
type CompressionPreview struct {
	OriginalSize     int64
	EstimatedSize    int64
	EstimatedRatio   float64
	CompressionType  string
	CompressionLevel string
	FileCount        int
	DirectoryCount   int
}

// EstimateCompression provides an estimate of compression results without actually compressing
func EstimateCompression(inputPath, compressionType, compressionLevel string) (*CompressionPreview, error) {
	info, err := os.Stat(inputPath)
	if err != nil {
		return nil, fmt.Errorf("failed to stat input: %w", err)
	}

	preview := &CompressionPreview{
		CompressionType:  compressionType,
		CompressionLevel: compressionLevel,
	}

	if info.IsDir() {
		// Calculate directory size and file count
		err = calculateDirectoryStats(inputPath, preview)
		if err != nil {
			return nil, fmt.Errorf("failed to calculate directory stats: %w", err)
		}
	} else {
		// Single file
		preview.OriginalSize = info.Size()
		preview.FileCount = 1
		preview.DirectoryCount = 0
	}

	// Estimate compression ratio based on compression type and level
	estimatedRatio := estimateCompressionRatio(compressionType, compressionLevel)
	preview.EstimatedRatio = estimatedRatio
	preview.EstimatedSize = int64(float64(preview.OriginalSize) * estimatedRatio)

	return preview, nil
}

// calculateDirectoryStats calculates the total size and counts for a directory
func calculateDirectoryStats(dirPath string, preview *CompressionPreview) error {
	var totalSize int64
	var fileCount, dirCount int

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			dirCount++
		} else {
			fileCount++
			totalSize += info.Size()
		}
		return nil
	})

	if err != nil {
		return err
	}

	preview.OriginalSize = totalSize
	preview.FileCount = fileCount
	preview.DirectoryCount = dirCount
	return nil
}

// estimateCompressionRatio provides estimated compression ratios based on format and level
func estimateCompressionRatio(compressionType, compressionLevel string) float64 {
	// These are rough estimates based on typical compression ratios
	// Actual ratios will vary significantly based on file content

	baseRatios := map[string]float64{
		"gzip":    0.35, // ~65% compression
		"xz":      0.25, // ~75% compression
		"bzip2":   0.30, // ~70% compression
		"zstd":    0.40, // ~60% compression
		"zip":     0.35, // ~65% compression
		"tar.gz":  0.35, // ~65% compression
		"tar.xz":  0.25, // ~75% compression
		"tar.bz2": 0.30, // ~70% compression
		"tar.zst": 0.40, // ~60% compression
	}

	baseRatio, exists := baseRatios[compressionType]
	if !exists {
		baseRatio = 0.50 // Default 50% compression
	}

	// Adjust based on compression level
	switch strings.ToLower(compressionLevel) {
	case "good":
		return baseRatio * 1.1 // Slightly worse compression, faster
	case "better":
		return baseRatio // Standard compression
	case "best":
		return baseRatio * 0.9 // Better compression, slower
	default:
		return baseRatio
	}
}

// FormatPreview formats the compression preview for display
func (p *CompressionPreview) FormatPreview() string {
	var result strings.Builder

	result.WriteString("Compression Preview\n")
	result.WriteString("==================\n\n")

	result.WriteString(fmt.Sprintf("Compression Type: %s\n", p.CompressionType))
	result.WriteString(fmt.Sprintf("Compression Level: %s\n", p.CompressionLevel))
	result.WriteString("\n")

	if p.DirectoryCount > 0 {
		result.WriteString(fmt.Sprintf("Files: %d\n", p.FileCount))
		result.WriteString(fmt.Sprintf("Directories: %d\n", p.DirectoryCount))
	} else {
		result.WriteString("Single file compression\n")
	}
	result.WriteString("\n")

	result.WriteString(fmt.Sprintf("Original Size: %s\n", formatSize(p.OriginalSize)))
	result.WriteString(fmt.Sprintf("Estimated Compressed Size: %s\n", formatSize(p.EstimatedSize)))
	result.WriteString(fmt.Sprintf("Estimated Compression Ratio: %.1f%%\n", (1.0-p.EstimatedRatio)*100))
	result.WriteString(fmt.Sprintf("Estimated Space Saved: %s\n", formatSize(p.OriginalSize-p.EstimatedSize)))
	result.WriteString("\n")

	result.WriteString("Note: These are estimates based on typical compression ratios.\n")
	result.WriteString("Actual results may vary depending on file content.\n")

	return result.String()
}
