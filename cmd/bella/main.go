package main

import (
	"flag"
	"fmt"
	"os"
	"strings"
	"time"

	"bella/pkg/copier"
	"bella/pkg/logging"
	"bella/pkg/ui"
)

func showHelp() {
	fmt.Println("Bella - Advanced Data Copier")
	fmt.Println()
	fmt.Println("USAGE:")
	fmt.Println("  bella                    Launch interactive UI")
	fmt.Println("  bella [options]          Run in command-line mode")
	fmt.Println("  bella -h, --help         Show this help")
	fmt.Println()
	fmt.Println("COPY OPTIONS:")
	fmt.Println("  -input string            Input file, directory, or device")
	fmt.Println("  -output string           Output file, directory, or device")
	fmt.Println("  -bs int                  Block size in bytes (default 4194304)")
	fmt.Println("  -compression string      Compression mode: none|compress|decompress|auto (default \"none\")")
	fmt.Println("  -compresstype string     Compression type: tar.gz|tar.xz|tar.bz2|tar.zst|zip|gzip|xz|bzip2|zstd (default \"tar.gz\")")
	fmt.Println("  -count int               Number of blocks to copy, -1 for all (default -1)")
	fmt.Println("  -skip int                Blocks to skip at start of input")
	fmt.Println("  -seek int                Blocks to seek at start of output")
	fmt.Println("  -sparse                  Enable sparse file copy")
	fmt.Println("  -skip-bad                Skip read errors and fill with zeros")
	fmt.Println("  -verify                  Verify copy by comparing source and destination *after* copy")
	fmt.Println("  -append                  Append to output file instead of overwriting")
	fmt.Println("  -checksum string         Calculate checksum using algorithm: sha256|md5|sha1")
	fmt.Println("  -no-offload              Disable kernel copy offload (enabled by default)")
	fmt.Println()
	fmt.Println("WIPE OPTIONS:")
	fmt.Println("  -wipe string             Wipe mode: zero|random")
	fmt.Println("  -passes int              Number of wipe passes (default 1)")
	fmt.Println()
	fmt.Println("VERIFY OPTIONS:")
	fmt.Println("  -verify-only             Verify source and destination are identical (no copy)")
	fmt.Println()
	fmt.Println("BROWSE OPTIONS:")
	fmt.Println("  -browse                  Browse archive contents without extracting")
	fmt.Println()
	fmt.Println("SELECTIVE EXTRACTION OPTIONS:")
	fmt.Println("  -extract-files           Extract specific files from archive (comma-separated list)")
	fmt.Println()
	fmt.Println("PREVIEW OPTIONS:")
	fmt.Println("  -preview                 Show compression preview with estimated ratios")
	fmt.Println()
	fmt.Println("PASSWORD PROTECTION OPTIONS:")
	fmt.Println("  -password                Password for encrypted archives (ZIP only)")
	fmt.Println()
	fmt.Println("CONFIGURATION PROFILE OPTIONS:")
	fmt.Println("  -profile                 Load settings from named profile")
	fmt.Println("  -save-profile            Save current settings as named profile")
	fmt.Println("  -list-profiles           List all available profiles")
	fmt.Println("  -create-defaults         Create default configuration profiles")
	fmt.Println()
	fmt.Println("CLEANUP OPTIONS:")
	fmt.Println("  -cleanup-temp            Remove old temporary files (older than 24 hours)")
	fmt.Println()
	fmt.Println("GENERAL OPTIONS:")
	fmt.Println("  -progress                Show progress bar (default true)")
	fmt.Println("  -dry-run                 Show what would be done without executing")
	fmt.Println()
	fmt.Println("EXAMPLES:")
	fmt.Println("  bella -input file.txt -output copy.txt")
	fmt.Println("  bella -input my_folder -output archive -compression compress -compresstype tar.xz")
	fmt.Println("  bella -input my_folder -output archive -compression compress -compresstype tar.bz2")
	fmt.Println("  bella -input my_folder -output archive -compression compress -compresstype tar.zst")
	fmt.Println("  bella -input file.gz -output file.txt -compression decompress")
	fmt.Println("  bella -input file.bz2 -output file.txt -compression decompress")
	fmt.Println("  bella -input file.zst -output file.txt -compression decompress")
	fmt.Println("  bella -input archive.tar.gz -browse")
	fmt.Println("  bella -input archive.tar.gz -output ./extracted -extract-files \"file1.txt,dir/file2.txt\"")
	fmt.Println("  bella -input /path/to/data -compression compress -compresstype tar.gz -preview")
	fmt.Println("  bella -input source.img -output copy.img -verify")
	fmt.Println("  bella -input source.img -output copy.img -verify-only")
	fmt.Println("  bella -input data.bin -output backup.bin -checksum sha256")
	fmt.Println("  bella -input new.txt -output existing.txt -append")
	fmt.Println("  bella -wipe random -output /dev/sdX")
}

func main() {

	logging.Init()

	for _, arg := range os.Args {
		if arg == "-h" || arg == "--help" {
			showHelp()
			return
		}
	}

	if len(os.Args) < 2 {
		app := ui.NewApp()
		if err := app.Run(); err != nil {
			fmt.Fprintf(os.Stderr, "UI Error: %v\n", err)
			os.Exit(1)
		}
		return
	}

	cfg := copier.NewConfig()
	var noOffload bool
	var verifyOnly bool
	var browseArchive bool
	var previewCompression bool
	var extractFiles string
	var profileName string
	var saveProfileName string
	var listProfiles bool
	var createDefaults bool
	var cleanupTemp bool

	fs := flag.NewFlagSet("bella", flag.ExitOnError)
	fs.Usage = showHelp

	fs.StringVar(&cfg.Input, "input", "", "Input file, directory, or device")
	fs.StringVar(&cfg.Output, "output", "", "Output file, directory, or device")
	fs.IntVar(&cfg.BlockSize, "bs", 4*1024*1024, "Block size in bytes")
	fs.BoolVar(&cfg.Progress, "progress", true, "Show progress bar")
	fs.IntVar(&cfg.Count, "count", -1, "Number of blocks to copy (-1 for all)")
	fs.Int64Var(&cfg.Skip, "skip", 0, "Blocks to skip at start of input")
	fs.Int64Var(&cfg.Seek, "seek", 0, "Blocks to seek at start of output")
	fs.StringVar(&cfg.Compression, "compression", "none", "Compression mode: compress|decompress|auto|none")
	fs.StringVar(&cfg.CompressionType, "compresstype", "tar.gz", "Compression type: tar.gz|tar.xz|tar.bz2|tar.zst|zip|gzip|xz|bzip2|zstd")
	fs.StringVar(&cfg.CompressionLevel, "compresslevel", "Good", "Compression level: Good|Better|Best")
	fs.StringVar(&cfg.Password, "password", "", "Password for encrypted archives (ZIP only)")
	fs.BoolVar(&cfg.Sparse, "sparse", false, "Enable sparse file copy")
	fs.BoolVar(&cfg.SkipBadSectors, "skip-bad", false, "Skip read errors and fill with zeros")
	fs.BoolVar(&cfg.Verify, "verify", false, "Verify copy by comparing source and destination *after* copy")
	fs.BoolVar(&cfg.Append, "append", false, "Append to output file instead of overwriting")
	fs.BoolVar(&noOffload, "no-offload", false, "Disable kernel copy offload (enabled by default)")
	fs.BoolVar(&cfg.DryRun, "dry-run", false, "Show what would be done without executing")
	fs.StringVar(&cfg.WipeMode, "wipe", "", "Wipe mode: 'zero'|'random'")
	fs.IntVar(&cfg.WipePasses, "passes", 1, "Number of passes for wipe operation")
	fs.StringVar(&cfg.Checksum, "checksum", "", "Calculate checksum using algorithm: sha256|md5|sha1")
	fs.BoolVar(&verifyOnly, "verify-only", false, "Verify source and destination are identical (no copy)")
	fs.BoolVar(&browseArchive, "browse", false, "Browse archive contents without extracting")
	fs.BoolVar(&previewCompression, "preview", false, "Show compression preview with estimated ratios")
	fs.StringVar(&extractFiles, "extract-files", "", "Extract specific files from archive (comma-separated list)")
	fs.StringVar(&profileName, "profile", "", "Load settings from named profile")
	fs.StringVar(&saveProfileName, "save-profile", "", "Save current settings as named profile")
	fs.BoolVar(&listProfiles, "list-profiles", false, "List all available profiles")
	fs.BoolVar(&createDefaults, "create-defaults", false, "Create default configuration profiles")
	fs.BoolVar(&cleanupTemp, "cleanup-temp", false, "Remove old temporary files (older than 24 hours)")

	fs.Parse(os.Args[1:])

	if noOffload {
		cfg.UseCopyOffload = false
	}

	// Handle configuration profile operations
	if createDefaults {
		config, err := copier.CreateDefaultProfiles()
		if err != nil {
			fmt.Fprintf(os.Stderr, "Error creating default profiles: %v\n", err)
			os.Exit(1)
		}
		if err := config.SaveConfigFile(); err != nil {
			fmt.Fprintf(os.Stderr, "Error saving default profiles: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("Default configuration profiles created successfully.")
		return
	}

	if listProfiles {
		config, err := copier.LoadConfigFile()
		if err != nil {
			fmt.Fprintf(os.Stderr, "Error loading config file: %v\n", err)
			os.Exit(1)
		}
		profiles := config.ListProfiles()
		if len(profiles) == 0 {
			fmt.Println("No profiles found. Use -create-defaults to create default profiles.")
		} else {
			fmt.Println("Available profiles:")
			for _, name := range profiles {
				profile, _ := config.GetProfile(name)
				fmt.Printf("  %s: %s\n", name, profile.Description)
			}
		}
		return
	}

	if cleanupTemp {
		fmt.Println("Cleaning up old temporary files...")
		if err := copier.CleanupOldTempFiles(24 * time.Hour); err != nil {
			fmt.Fprintf(os.Stderr, "Error cleaning up temporary files: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("Temporary file cleanup completed successfully.")
		return
	}

	// Load profile if specified
	if profileName != "" {
		config, err := copier.LoadConfigFile()
		if err != nil {
			fmt.Fprintf(os.Stderr, "Error loading config file: %v\n", err)
			os.Exit(1)
		}
		profile, err := config.GetProfile(profileName)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Error loading profile '%s': %v\n", profileName, err)
			os.Exit(1)
		}
		profile.ApplyProfile(cfg)
		fmt.Printf("Loaded profile: %s\n", profileName)
	}

	hasInput := cfg.Input != ""
	hasOutput := cfg.Output != ""
	isWipe := cfg.WipeMode != ""

	if verifyOnly {
		if !hasInput || !hasOutput {
			fmt.Fprintln(os.Stderr, "Error: -verify-only requires both -input and -output to be specified.")
			os.Exit(1)
		}
		cfg.Operation = copier.OpVerify
	} else if browseArchive {
		if !hasInput {
			fmt.Fprintln(os.Stderr, "Error: -browse requires -input to be specified.")
			os.Exit(1)
		}
		cfg.Operation = copier.OpBrowse
	} else if previewCompression {
		if !hasInput {
			fmt.Fprintln(os.Stderr, "Error: -preview requires -input to be specified.")
			os.Exit(1)
		}
		if cfg.Compression != "compress" {
			fmt.Fprintln(os.Stderr, "Error: -preview requires -compression compress to be specified.")
			os.Exit(1)
		}
		// Show compression preview and exit
		preview, err := copier.EstimateCompression(cfg.Input, cfg.CompressionType, cfg.CompressionLevel)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Error generating compression preview: %v\n", err)
			os.Exit(1)
		}
		fmt.Print(preview.FormatPreview())
		return
	} else if extractFiles != "" {
		if !hasInput {
			fmt.Fprintln(os.Stderr, "Error: -extract-files requires -input to be specified.")
			os.Exit(1)
		}
		if !hasOutput {
			fmt.Fprintln(os.Stderr, "Error: -extract-files requires -output to be specified.")
			os.Exit(1)
		}
		cfg.Operation = copier.OpSelectiveExtract
		// Parse comma-separated file list
		files := strings.Split(extractFiles, ",")
		for i, file := range files {
			files[i] = strings.TrimSpace(file)
		}
		cfg.SelectedFiles = files
	} else if isWipe {
		cfg.Operation = copier.OpWipe
	} else if hasInput && hasOutput {
		cfg.Operation = copier.OpCopy
	} else {
		fmt.Fprintln(os.Stderr, "Error: Invalid or incomplete flags specified.")
		fmt.Fprintln(os.Stderr, "Please specify an operation (-input/-output for copy, -wipe, -verify-only, or -browse).")
		fmt.Fprintln(os.Stderr, "Use -h or --help for more information.")
		os.Exit(1)
	}

	if err := cfg.Validate(); err != nil {
		fmt.Fprintf(os.Stderr, "Configuration Error: %v\n", err)
		os.Exit(1)
	}

	if err := ui.RunCLIProgress(cfg); err != nil {
		fmt.Fprintf(os.Stderr, "\nExecution Error: %v\n", err)
		os.Exit(1)
	}

	// Save profile if requested
	if saveProfileName != "" {
		config, err := copier.LoadConfigFile()
		if err != nil {
			fmt.Fprintf(os.Stderr, "Warning: Could not load config file to save profile: %v\n", err)
			return
		}

		description := fmt.Sprintf("Custom profile saved on %s", time.Now().Format("2006-01-02 15:04:05"))
		profile := copier.ConfigToProfile(cfg, saveProfileName, description)

		if err := config.AddProfile(profile); err != nil {
			fmt.Fprintf(os.Stderr, "Warning: Could not add profile: %v\n", err)
			return
		}

		if err := config.SaveConfigFile(); err != nil {
			fmt.Fprintf(os.Stderr, "Warning: Could not save profile: %v\n", err)
			return
		}

		fmt.Printf("Profile '%s' saved successfully.\n", saveProfileName)
	}
}
